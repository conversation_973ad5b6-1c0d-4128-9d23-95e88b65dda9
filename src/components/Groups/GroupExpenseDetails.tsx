import React from 'react';
import { X, <PERSON>, Calculator, ArrowRight, ArrowLeft } from 'lucide-react';
import { getCategoryIcon } from '../../services/expenseService';
import type { GroupExpense, User } from '../../types';

interface GroupExpenseDetailsProps {
  expense: GroupExpense;
  members: User[];
  currentUserId: string;
  onClose: () => void;
}

const GroupExpenseDetails: React.FC<GroupExpenseDetailsProps> = ({
  expense,
  members,
  currentUserId,
  onClose
}) => {
  // Calculate who owes what
  const paidByUser = members.find(m => m.uid === expense.paidBy);
  const splitDetails = expense.splitDetails || {};
  
  // Calculate balances
  const balances = Object.entries(splitDetails).map(([userId, amount]) => {
    const user = members.find(m => m.uid === userId);
    const isPayer = userId === expense.paidBy;
    const owedAmount = isPayer ? expense.amount - amount : amount;
    
    return {
      user,
      owedAmount: isPayer ? -(expense.amount - amount) : amount,
      isPayer,
      netBalance: isPayer ? expense.amount - amount : -amount
    };
  }).filter(b => b.user);

  const formatCurrency = (amount: number) => `₹${Math.abs(amount).toLocaleString()}`;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
          <h2 className="font-semibold text-gray-900 dark:text-white">Expense Details</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          >
            <X size={16} className="text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        <div className="p-3 space-y-4">
          {/* Expense Info */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                <span className="text-lg">{getCategoryIcon(expense.category)}</span>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 dark:text-white">{expense.title}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {expense.date.toLocaleDateString()} • {expense.category}
                </p>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(expense.amount)}
                </div>
              </div>
            </div>
            {expense.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                {expense.description}
              </p>
            )}
          </div>

          {/* Paid By */}
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Paid By</h4>
            <div className="flex items-center gap-2 text-sm">
              <Users size={14} className="text-gray-500" />
              <span className="text-gray-700 dark:text-gray-300">
                {paidByUser?.displayName || 'Unknown User'}
              </span>
              <span className="text-green-600 dark:text-green-400 font-medium">
                {formatCurrency(expense.amount)}
              </span>
            </div>
          </div>

          {/* Split Details */}
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Split Details</h4>
            <div className="space-y-2">
              {balances.map((balance, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-xs text-white font-medium">
                        {balance.user?.displayName?.charAt(0) || '?'}
                      </span>
                    </div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {balance.user?.displayName}
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-400">owes </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(Math.abs(balance.owedAmount))}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Settlement Summary */}
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Who Owes What</h4>
            <div className="space-y-2">
              {balances
                .filter(b => b.netBalance !== 0 && !b.isPayer)
                .map((balance, index) => (
                  <div key={index} className="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-600 rounded">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {balance.user?.displayName}
                      </span>
                      <ArrowRight size={14} className="text-gray-400" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {paidByUser?.displayName}
                      </span>
                    </div>
                    <span className="font-medium text-red-600 dark:text-red-400">
                      {formatCurrency(Math.abs(balance.netBalance))}
                    </span>
                  </div>
                ))}
            </div>
          </div>

          {/* Your Share */}
          {splitDetails[currentUserId] && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Your Share</h4>
              <div className="text-sm text-blue-700 dark:text-blue-300">
                You owe {formatCurrency(splitDetails[currentUserId])}
                {expense.paidBy === currentUserId && (
                  <span className="block mt-1 text-green-700 dark:text-green-300">
                    You paid {formatCurrency(expense.amount)} • 
                    You're owed {formatCurrency(expense.amount - splitDetails[currentUserId])}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <button
              onClick={onClose}
              className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
            >
              Close
            </button>
            {expense.paidBy !== currentUserId && splitDetails[currentUserId] && (
              <button className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors text-sm">
                Settle Up
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GroupExpenseDetails;
