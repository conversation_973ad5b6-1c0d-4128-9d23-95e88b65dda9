import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  serverTimestamp,
  writeBatch 
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { activityService } from './firestore';
import type { ExpenseCategory } from '../types';

export interface SingleExpense {
  id: string;
  title: string;
  description?: string;
  amount: number;
  category: ExpenseCategory;
  paidBy: string; // User UID who paid
  splitWith: string; // User UID to split with
  splitType: 'equal' | 'manual';
  splitDetails: {
    [userId: string]: number; // Amount each person owes
  };
  date: Date;
  createdAt: Date;
  updatedAt: Date;
  isSettled: boolean;
}

export const singleExpenseService = {
  // Create a single expense between two people
  async create(expenseData: {
    title: string;
    description?: string;
    amount: number;
    category: ExpenseCategory;
    paidBy: string;
    splitWith: string;
    splitType: 'equal' | 'manual';
    splitDetails: { [userId: string]: number };
    date: Date;
  }): Promise<string> {
    try {
      const expensesRef = collection(db, 'splitwise-expenses');
      
      const docRef = await addDoc(expensesRef, {
        ...expenseData,
        type: 'single', // Mark as single expense
        splitBetween: [expenseData.paidBy, expenseData.splitWith],
        isSettled: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Log activity for both users
      const otherUserId = expenseData.splitWith;
      const otherUserAmount = expenseData.splitDetails[otherUserId] || 0;

      await activityService.log({
        userId: expenseData.paidBy,
        action: 'added_expense',
        entityType: 'expense',
        entityId: docRef.id,
        details: `Added expense: ${expenseData.title} - ₹${expenseData.amount} (₹${otherUserAmount} owed by other person)`,
        metadata: {
          amount: expenseData.amount,
          category: expenseData.category,
          debtAmount: otherUserAmount,
          splitWith: otherUserId,
          type: 'single'
        }
      });

      return docRef.id;
    } catch (error) {
      console.error('Error creating single expense:', error);
      throw error;
    }
  },

  // Get all single expenses for a user
  async getUserExpenses(userId: string): Promise<SingleExpense[]> {
    try {
      const expensesRef = collection(db, 'splitwise-expenses');
      const snapshot = await getDocs(expensesRef);
      
      const expenses = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        date: doc.data().date?.toDate() || new Date(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as SingleExpense[];

      // Filter for single expenses involving this user
      return expenses
        .filter(expense => 
          expense.splitBetween?.includes(userId) && 
          (expense as any).type === 'single'
        )
        .sort((a, b) => b.date.getTime() - a.date.getTime());
    } catch (error) {
      console.error('Error fetching user expenses:', error);
      throw error;
    }
  },

  // Get expenses between two specific users
  async getExpensesBetweenUsers(user1Id: string, user2Id: string): Promise<SingleExpense[]> {
    try {
      const expensesRef = collection(db, 'splitwise-expenses');
      const snapshot = await getDocs(expensesRef);
      
      const expenses = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        date: doc.data().date?.toDate() || new Date(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as SingleExpense[];

      // Filter for expenses between these two users
      return expenses
        .filter(expense => {
          const splitBetween = expense.splitBetween || [];
          return (expense as any).type === 'single' &&
                 splitBetween.includes(user1Id) && 
                 splitBetween.includes(user2Id);
        })
        .sort((a, b) => b.date.getTime() - a.date.getTime());
    } catch (error) {
      console.error('Error fetching expenses between users:', error);
      throw error;
    }
  },

  // Update an expense
  async update(id: string, updates: Partial<SingleExpense>): Promise<void> {
    try {
      const expenseRef = doc(db, 'splitwise-expenses', id);
      await updateDoc(expenseRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating expense:', error);
      throw error;
    }
  },

  // Delete an expense
  async delete(id: string): Promise<void> {
    try {
      const expenseRef = doc(db, 'splitwise-expenses', id);
      await deleteDoc(expenseRef);
    } catch (error) {
      console.error('Error deleting expense:', error);
      throw error;
    }
  },

  // Settle an expense
  async settle(id: string, settledBy: string): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      const expenseRef = doc(db, 'splitwise-expenses', id);
      batch.update(expenseRef, {
        isSettled: true,
        settledAt: serverTimestamp(),
        settledBy: settledBy,
        updatedAt: serverTimestamp()
      });

      await batch.commit();

      // Log settlement activity
      await activityService.log({
        userId: settledBy,
        action: 'settled_expense',
        entityType: 'expense',
        entityId: id,
        details: `Settled expense`,
        metadata: {
          settledBy: settledBy
        }
      });
    } catch (error) {
      console.error('Error settling expense:', error);
      throw error;
    }
  }
};
