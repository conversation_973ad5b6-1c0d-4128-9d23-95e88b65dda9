import React, { useState } from 'react';
import { X, Users } from 'lucide-react';
import type { Group } from '../../types';

interface CreateGroupModalProps {
  onClose: () => void;
  onCreateGroup: (groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  currentUserId: string;
}

const CreateGroupModal: React.FC<CreateGroupModalProps> = ({
  onClose,
  onCreateGroup,
  currentUserId
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      setError('Group name is required');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'> = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        members: [currentUserId],
        createdBy: currentUserId,
        totalExpenses: 0,
        isActive: true
      };

      await onCreateGroup(groupData);
      onClose();
    } catch (err) {
      setError('Failed to create group. Please try again.');
      console.error('Error creating group:', err);
    } finally {
      setLoading(false);
    }
  };

  const addEmailField = () => {
    setMemberEmails([...memberEmails, '']);
    // Focus the new input after a short delay
    setTimeout(() => {
      const newIndex = memberEmails.length;
      emailInputRefs.current[newIndex]?.focus();
    }, 100);
  };

  const removeEmailField = (index: number) => {
    if (memberEmails.length > 1) {
      setMemberEmails(memberEmails.filter((_, i) => i !== index));
      emailInputRefs.current = emailInputRefs.current.filter((_, i) => i !== index);
    }
  };

  const updateEmail = (index: number, email: string) => {
    const updated = [...memberEmails];
    updated[index] = email;
    setMemberEmails(updated);

    // Generate email suggestions
    if (email.includes('@') && !email.includes('.')) {
      const [username] = email.split('@');
      const suggestions = COMMON_EMAIL_SUGGESTIONS.map(domain => `${username}@${domain}`);
      setEmailSuggestions(suggestions);
      setActiveEmailIndex(index);
    } else {
      setEmailSuggestions([]);
      setActiveEmailIndex(null);
    }
  };

  const selectEmailSuggestion = (suggestion: string, index: number) => {
    const updated = [...memberEmails];
    updated[index] = suggestion;
    setMemberEmails(updated);
    setEmailSuggestions([]);
    setActiveEmailIndex(null);
  };

  const addQuickEmail = (email: string) => {
    if (email && !memberEmails.includes(email)) {
      setMemberEmails([...memberEmails.filter(e => e.trim()), email, '']);
    }
  };

  const addUserFromSearch = (userEmail: string) => {
    if (!memberEmails.includes(userEmail)) {
      setMemberEmails([...memberEmails.filter(e => e.trim()), userEmail, '']);
      setUserSearchQuery('');
      setShowUserSearch(false);
    }
  };

  // Get existing users for suggestions
  const existingUserEmails = users.slice(0, 5).map(user => user.email);

  // Search results
  const searchResults = userSearchQuery.trim() ? searchUsers(userSearchQuery).slice(0, 5) : [];

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto border border-gray-200 dark:border-gray-700">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Users size={20} className="text-white" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">Create New Group</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-200 hover:scale-105"
          >
            <X size={20} className="text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Form Content */}
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 rounded-xl text-sm">
                {error}
              </div>
            )}

            {/* Group Name */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                Group Name *
              </label>
              <div className="relative">
                <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full pl-10 pr-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="e.g., Trip to Goa, Office Lunch"
                  required
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                placeholder="What's this group for? (optional)"
                rows={3}
              />
            </div>

            {/* Member Emails */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                Add Members
              </label>

              {/* Creator Auto-included Notice */}
              <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                  <Check size={16} />
                  <span className="text-sm font-medium">You are automatically included as a group member</span>
                </div>
              </div>

              {/* Search Existing Users */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-xs text-gray-500 dark:text-gray-400">Add existing users:</p>
                  <button
                    type="button"
                    onClick={() => setShowUserSearch(!showUserSearch)}
                    className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    {showUserSearch ? 'Hide search' : 'Search users'}
                  </button>
                </div>

                {showUserSearch && (
                  <div className="relative mb-3">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                    <input
                      type="text"
                      value={userSearchQuery}
                      onChange={(e) => setUserSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                      placeholder="Search by name or email..."
                    />

                    {searchResults.length > 0 && (
                      <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg max-h-40 overflow-y-auto">
                        {searchResults.map((user) => (
                          <button
                            key={user.uid}
                            type="button"
                            onClick={() => addUserFromSearch(user.email)}
                            className="w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                          >
                            <div className="flex items-center gap-3">
                              {user.photoURL ? (
                                <img src={user.photoURL} alt={user.displayName} className="w-6 h-6 rounded-full" />
                              ) : (
                                <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                  <span className="text-xs text-white font-semibold">
                                    {user.displayName.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              )}
                              <div>
                                <p className="text-sm font-medium">{user.displayName}</p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">{user.email}</p>
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Quick add existing users */}
                <div className="flex flex-wrap gap-2">
                  {existingUserEmails.slice(0, 3).map((email, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => addQuickEmail(email)}
                      className="flex items-center gap-1 px-3 py-1 bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-xs hover:bg-green-100 dark:hover:bg-green-900/50 transition-all duration-200 hover:scale-105"
                    >
                      <Plus size={12} />
                      {email.split('@')[0]}
                    </button>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                {memberEmails.map((email, index) => (
                  <div key={index} className="relative">
                    <div className="flex gap-3">
                      <div className="flex-1 relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                        <input
                          ref={(el) => (emailInputRefs.current[index] = el)}
                          type="email"
                          value={email}
                          onChange={(e) => updateEmail(index, e.target.value)}
                          className="w-full pl-10 pr-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          placeholder="<EMAIL>"
                        />
                        {email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email) && (
                          <Check className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500" size={16} />
                        )}
                      </div>
                      {memberEmails.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeEmailField(index)}
                          className="p-3 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-all duration-200 hover:scale-105"
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
                    </div>

                    {/* Email Suggestions */}
                    {activeEmailIndex === index && emailSuggestions.length > 0 && (
                      <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl shadow-lg max-h-40 overflow-y-auto">
                        {emailSuggestions.map((suggestion, suggestionIndex) => (
                          <button
                            key={suggestionIndex}
                            type="button"
                            onClick={() => selectEmailSuggestion(suggestion, index)}
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm"
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}

                <button
                  type="button"
                  onClick={addEmailField}
                  className="flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 p-3 rounded-xl transition-all duration-200 hover:scale-105 w-full justify-center border-2 border-dashed border-blue-300 dark:border-blue-600"
                >
                  <Plus size={16} />
                  Add another member
                </button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-6">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 hover:scale-[1.02]"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-200 hover:scale-[1.02] disabled:hover:scale-100 shadow-lg"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Creating...
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <Users size={16} />
                    Create Group
                  </div>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateGroupModal;
