export interface User {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  isAdmin: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Group {
  id: string;
  name: string;
  description?: string;
  members: string[]; // User UIDs
  memberEmails?: string[]; // Email addresses for pending invitations
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  totalExpenses: number;
  isActive: boolean;
}

export interface Settlement {
  id: string;
  groupId: string;
  fromUserId: string;
  toUserId: string;
  amount: number;
  settledAt: Date;
  description?: string;
}

export interface Expense {
  id: string;
  title: string;
  description?: string;
  amount: number;
  category: ExpenseCategory;
  type: 'group' | 'single' | 'personal';
  groupId?: string;
  paidBy: string; // User UID
  splitBetween: string[]; // User UIDs
  splitType: 'equal' | 'exact' | 'percentage';
  splitDetails?: { [userId: string]: number };
  date: Date;
  createdAt: Date;
  updatedAt: Date;
  isSettled: boolean;
}

export interface PersonalExpense {
  id: string;
  title: string;
  description?: string;
  amount: number;
  category: ExpenseCategory;
  userId: string;
  date: Date;
  month: string; // Format: YYYY-MM
  year: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ActivityLog {
  id: string;
  userId: string;
  action: ActivityAction;
  entityType: 'group' | 'expense' | 'user' | 'personal_expense';
  entityId: string;
  details: string;
  metadata?: { [key: string]: any };
  timestamp: Date;
}

export interface Balance {
  userId: string;
  groupId?: string;
  owes: { [userId: string]: number };
  owedBy: { [userId: string]: number };
  netBalance: number;
}

export interface Statistics {
  userId: string;
  totalExpenses: number;
  totalSpent: number;
  totalOwed: number;
  totalOwing: number;
  categoryBreakdown: { [category: string]: number };
  monthlySpending: { [month: string]: number };
  friendsOwing: { [userId: string]: number };
  friendsOwed: { [userId: string]: number };
  lastUpdated: Date;
}

export type ExpenseCategory = 
  | 'hotel' 
  | 'juice' 
  | 'petrol' 
  | 'cloth' 
  | 'medicine' 
  | 'bills' 
  | 'food' 
  | 'transport' 
  | 'entertainment' 
  | 'shopping' 
  | 'utilities' 
  | 'other';

export type ActivityAction = 
  | 'created_group' 
  | 'joined_group' 
  | 'left_group' 
  | 'added_expense' 
  | 'updated_expense' 
  | 'deleted_expense' 
  | 'settled_expense' 
  | 'added_personal_expense' 
  | 'updated_personal_expense' 
  | 'deleted_personal_expense';

export type Theme = 'light' | 'dark';

export interface AppState {
  user: User | null;
  theme: Theme;
  loading: boolean;
  error: string | null;
}

export interface GroupMember extends User {
  balance: number;
  totalPaid: number;
  totalOwed: number;
}
