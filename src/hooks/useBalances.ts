import { useState, useEffect } from 'react';
import { balanceService } from '../services/balanceService';
import type { Balance } from '../types';

export const useGroupBalances = (groupId: string | null) => {
  const [balances, setBalances] = useState<Record<string, Balance>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!groupId) {
      setBalances({});
      setLoading(false);
      return;
    }

    const fetchBalances = async () => {
      try {
        setLoading(true);
        setError(null);
        const groupBalances = await balanceService.calculateGroupBalances(groupId);
        setBalances(groupBalances);
      } catch (err) {
        setError('Failed to load balances');
        console.error('Error fetching balances:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchBalances();
  }, [groupId]);

  const settleUp = async (fromUserId: string, toUserId: string, amount: number, description?: string) => {
    if (!groupId) return;
    
    try {
      await balanceService.settleUp(groupId, fromUserId, toUserId, amount, description);
      // Refresh balances after settlement
      const updatedBalances = await balanceService.calculateGroupBalances(groupId);
      setBalances(updatedBalances);
    } catch (err) {
      throw new Error('Failed to settle up');
    }
  };

  const canDeleteGroup = () => {
    return balanceService.canDeleteGroup(balances);
  };

  const getSimplifiedDebts = () => {
    return balanceService.getSimplifiedDebts(balances);
  };

  return {
    balances,
    loading,
    error,
    settleUp,
    canDeleteGroup,
    getSimplifiedDebts,
    refresh: () => {
      if (groupId) {
        balanceService.calculateGroupBalances(groupId).then(setBalances);
      }
    }
  };
};
