import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Users } from 'lucide-react';
import { useGroups } from '../../hooks/useGroups';
import { useAuth } from '../../contexts/AuthContext';
import { balanceService } from '../../services/balanceService';
import { userService } from '../../services/firestore';
import LoadingSpinner from '../UI/LoadingSpinner';
import CreateGroupModal from './CreateGroupModalSimple';
import type { Balance, User } from '../../types';

const Groups: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { groups, loading, error, createGroup } = useGroups();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [groupBalances, setGroupBalances] = useState<Record<string, Record<string, Balance>>>({});
  const [users, setUsers] = useState<Record<string, User>>({});

  // Load balances for all groups
  useEffect(() => {
    const loadGroupBalances = async () => {
      if (!groups.length) return;

      const balancesPromises = groups.map(async (group) => {
        try {
          const balances = await balanceService.calculateGroupBalances(group.id);
          return { groupId: group.id, balances };
        } catch (error) {
          console.error(`Error loading balances for group ${group.id}:`, error);
          return { groupId: group.id, balances: {} };
        }
      });

      const results = await Promise.all(balancesPromises);
      const balancesMap: Record<string, Record<string, Balance>> = {};
      results.forEach(({ groupId, balances }) => {
        balancesMap[groupId] = balances;
      });
      setGroupBalances(balancesMap);
    };

    const loadUsers = async () => {
      try {
        const allUsers = await userService.getAll();
        const usersMap: Record<string, User> = {};
        allUsers.forEach(user => {
          usersMap[user.uid] = user;
        });
        setUsers(usersMap);
      } catch (error) {
        console.error('Error loading users:', error);
      }
    };

    if (groups.length > 0) {
      loadGroupBalances();
      loadUsers();
    }
  }, [groups]);

  // Get top 2 people you owe or who owe you in a group
  const getTopDebts = (groupId: string) => {
    const balances = groupBalances[groupId];
    if (!balances || !currentUser) return { youOwe: [], owesYou: [] };

    const currentUserBalance = balances[currentUser.uid];
    if (!currentUserBalance) return { youOwe: [], owesYou: [] };

    // People you owe money to
    const youOwe = Object.entries(currentUserBalance.owes)
      .map(([userId, amount]) => ({
        user: users[userId],
        amount
      }))
      .filter(debt => debt.user && debt.amount > 0)
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 2);

    // People who owe you money
    const owesYou = Object.entries(currentUserBalance.owedBy)
      .map(([userId, amount]) => ({
        user: users[userId],
        amount
      }))
      .filter(debt => debt.user && debt.amount > 0)
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 2);

    return { youOwe, owesYou };
  };

  if (loading) {
    return <LoadingSpinner text="Loading groups..." />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Groups
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            Manage your group expenses and split bills with friends
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <Plus size={20} />
          Create Group
        </button>
      </div>

      {/* Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {groups.map((group) => (
          <div key={group.id} className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] border border-gray-200 dark:border-gray-700 overflow-hidden cursor-pointer">
            {/* Group Header */}
            <div className="p-6 border-b border-gray-100 dark:border-gray-700">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <Users size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 dark:text-white text-lg">{group.name}</h3>
                    {group.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{group.description}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Group Stats */}
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Expenses</span>
                <div className="flex items-center gap-1 text-green-600 dark:text-green-400 font-bold text-lg">
                  <span className="text-green-600 dark:text-green-400">₹</span>
                  {group.totalExpenses.toLocaleString()}
                </div>
              </div>

              {/* Debt Information */}
              {(() => {
                const debts = getTopDebts(group.id);
                const hasDebts = debts.youOwe.length > 0 || debts.owesYou.length > 0;



                if (!hasDebts) {
                  return (
                    <div className="text-center py-2">
                      <span className="text-sm text-gray-500 dark:text-gray-400">All settled up!</span>
                    </div>
                  );
                }

                return (
                  <div className="space-y-2">
                    {debts.youOwe.length > 0 && (
                      <div>
                        <div className="text-xs font-medium text-red-600 dark:text-red-400 mb-1">You owe:</div>
                        {debts.youOwe.map((debt, index) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <span className="text-gray-700 dark:text-gray-300 truncate">
                              {debt.user.displayName || debt.user.email}
                            </span>
                            <span className="text-red-600 dark:text-red-400 font-semibold">
                              ₹{debt.amount.toLocaleString()}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}

                    {debts.owesYou.length > 0 && (
                      <div>
                        <div className="text-xs font-medium text-green-600 dark:text-green-400 mb-1">Owes you:</div>
                        {debts.owesYou.map((debt, index) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <span className="text-gray-700 dark:text-gray-300 truncate">
                              {debt.user.displayName || debt.user.email}
                            </span>
                            <span className="text-green-600 dark:text-green-400 font-semibold">
                              ₹{debt.amount.toLocaleString()}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>

            {/* Action Buttons */}
            <div className="p-6 pt-0">
              <div className="flex gap-3">
                <button
                  onClick={() => navigate(`/groups/${group.id}`)}
                  className="flex-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 py-3 px-4 rounded-xl text-sm font-semibold hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-all duration-200 hover:scale-105 border border-blue-200 dark:border-blue-800"
                >
                  View Details
                </button>
                <button
                  onClick={() => navigate(`/groups/${group.id}`)}
                  className="flex-1 bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 py-3 px-4 rounded-xl text-sm font-semibold hover:bg-green-100 dark:hover:bg-green-900/50 transition-all duration-200 hover:scale-105 border border-green-200 dark:border-green-800"
                >
                  Add Expense
                </button>
              </div>
            </div>
          </div>
        ))}

        {/* Empty state */}
        {groups.length === 0 && (
          <div className="col-span-full">
            <div className="text-center py-16 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl border-2 border-dashed border-blue-200 dark:border-gray-600">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Users size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">No groups yet</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
                Create your first group to start tracking expenses with friends and family. Split bills, track spending, and keep everyone in sync!
              </p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <div className="flex items-center gap-2">
                  <Plus size={20} />
                  Create Your First Group
                </div>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Create Group Modal */}
      {showCreateForm && (
        <CreateGroupModal
          onClose={() => setShowCreateForm(false)}
          onCreateGroup={createGroup}
          currentUserId={currentUser?.uid || ''}
        />
      )}
    </div>
  );
};

export default Groups;
