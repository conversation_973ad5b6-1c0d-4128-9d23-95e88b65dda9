import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  Users, 
  Receipt, 
  Wallet, 
  Activity, 
  User 
} from 'lucide-react';

const Navigation: React.FC = () => {
  const navItems = [
    {
      to: '/groups',
      icon: Users,
      label: 'Groups',
      description: 'Manage group expenses'
    },
    {
      to: '/single',
      icon: Receipt,
      label: 'Single Expense',
      description: 'Add individual expenses'
    },
    {
      to: '/personal',
      icon: Wallet,
      label: 'Personal',
      description: 'Track personal expenses'
    },
    {
      to: '/activity',
      icon: Activity,
      label: 'Activity',
      description: 'View activity log'
    },
    {
      to: '/account',
      icon: User,
      label: 'Account',
      description: 'Account details'
    }
  ];

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden lg:block w-72 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-xl border-r border-gray-200 dark:border-gray-700 min-h-screen">
        <div className="p-6">
          <div className="space-y-3">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <NavLink
                  key={item.to}
                  to={item.to}
                  className={({ isActive }) =>
                    `flex items-center p-4 rounded-2xl transition-all duration-300 group transform hover:scale-[1.02] ${
                      isActive
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50 hover:shadow-md'
                    }`
                  }
                >
                  {({ isActive }) => (
                    <>
                      <div className={`p-2 rounded-xl mr-4 ${
                        isActive
                          ? 'bg-white/20'
                          : 'bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600'
                      }`}>
                        <Icon size={20} />
                      </div>
                      <div className="flex-1">
                        <div className="font-semibold text-sm">{item.label}</div>
                        <div className={`text-xs mt-1 ${
                          isActive
                            ? 'text-white/80'
                            : 'text-gray-500 dark:text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'
                        }`}>
                          {item.description}
                        </div>
                      </div>
                    </>
                  )}
                </NavLink>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Mobile Bottom Navigation */}
      <nav className="lg:hidden fixed bottom-0 left-0 right-0 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border-t border-gray-200 dark:border-gray-700 z-50">
        <div className="flex justify-around items-center py-1">
          {navItems.map((item) => {
            const Icon = item.icon;
            return (
              <NavLink
                key={item.to}
                to={item.to}
                className={({ isActive }) =>
                  `flex flex-col items-center p-2 rounded-lg transition-all duration-200 min-w-0 flex-1 ${
                    isActive
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <div className={`p-1.5 rounded transition-all duration-200 ${
                      isActive
                        ? 'bg-blue-100 dark:bg-blue-900/30'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}>
                      <Icon size={16} />
                    </div>
                    <span className="text-xs font-medium mt-0.5 truncate">{item.label}</span>
                  </>
                )}
              </NavLink>
            );
          })}
        </div>
      </nav>
    </>
  );
};

export default Navigation;
