import React, { useState, useCallback, useEffect } from 'react';
import { Users, Plus, Receipt, Calculator, ArrowRight, Calendar, Tag, UserPlus, X } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { usePeopleExpenses } from '../../hooks/usePeopleExpenses';
import { useUsers } from '../../hooks/useUsers';
import LoadingSpinner from '../UI/LoadingSpinner';
import { getCategoryIcon } from '../../services/expenseService';
import type { Expense } from '../../types';

const PeopleExpenses: React.FC = () => {
  const { currentUser } = useAuth();
  const { people, loading, error, getExpensesWithPerson, getRecentExpenses } = usePeopleExpenses();
  const { users, searchUsers } = useUsers();
  const [showAddPerson, setShowAddPerson] = useState(false);
  const [selectedPersonId, setSelectedPersonId] = useState<string | null>(null);
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<typeof users>([]);

  // Optimized helper functions
  const formatCurrency = useCallback((amount: number) => {
    return `₹${amount.toLocaleString()}`;
  }, []);

  const getBalanceColor = useCallback((balance: number) => {
    if (balance > 0) return 'text-green-600 dark:text-green-400';
    if (balance < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  }, []);

  const getBalanceText = useCallback((balance: number) => {
    if (balance > 0) return `₹${balance} owed to you`;
    if (balance < 0) return `₹${Math.abs(balance)} you owe`;
    return 'Settled up';
  }, []);

  // Search functionality
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      const results = searchUsers(query).filter(user =>
        user.uid !== currentUser?.uid &&
        !people.some(p => p.userId === user.uid)
      );
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  }, [searchUsers, currentUser?.uid, people]);

  const selectedPerson = selectedPersonId ? people.find(p => p.userId === selectedPersonId) : null;
  const recentExpenses = getRecentExpenses(5);

  // Calculate summary statistics
  const totalOwed = people.reduce((sum, person) => sum + person.totalOwed, 0);
  const totalOwing = people.reduce((sum, person) => sum + person.totalOwing, 0);
  const netBalance = totalOwed - totalOwing;

  // Keyboard navigation and escape key handling
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (selectedExpense) {
          setSelectedExpense(null);
        } else if (selectedPersonId) {
          setSelectedPersonId(null);
        } else if (showAddPerson) {
          setShowAddPerson(false);
          setSearchQuery('');
          setSearchResults([]);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedExpense, selectedPersonId, showAddPerson]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner text="Loading people and expenses..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center max-w-md">
          <Receipt size={48} className="mx-auto text-red-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Failed to Load Data
          </h3>
          <p className="text-red-600 dark:text-red-400 mb-4 text-sm">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">People & Expenses</h1>
          <p className="text-sm text-gray-600 dark:text-gray-400">Manage expenses with different people</p>
        </div>
        <button
          onClick={() => setShowAddPerson(true)}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          <UserPlus size={16} />
          Add Person
        </button>
      </div>

      {/* Summary Cards */}
      {people.length > 0 && (
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-green-700 dark:text-green-300">
              {formatCurrency(totalOwed)}
            </div>
            <div className="text-xs text-green-600 dark:text-green-400">
              Total Owed to You
            </div>
          </div>
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-red-700 dark:text-red-300">
              {formatCurrency(totalOwing)}
            </div>
            <div className="text-xs text-red-600 dark:text-red-400">
              Total You Owe
            </div>
          </div>
          <div className={`rounded-lg p-3 text-center ${
            netBalance > 0
              ? 'bg-green-50 dark:bg-green-900/20'
              : netBalance < 0
              ? 'bg-red-50 dark:bg-red-900/20'
              : 'bg-gray-50 dark:bg-gray-800'
          }`}>
            <div className={`text-lg font-bold ${getBalanceColor(netBalance)}`}>
              {formatCurrency(Math.abs(netBalance))}
            </div>
            <div className={`text-xs ${getBalanceColor(netBalance)}`}>
              Net {netBalance >= 0 ? 'Owed' : 'Owing'}
            </div>
          </div>
        </div>
      )}

      {/* People List */}
      {people.length === 0 ? (
        <div className="text-center py-12">
          <Users size={48} className="mx-auto text-gray-400 dark:text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No shared expenses yet</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Start adding expenses with friends to see them here
          </p>
          <button
            onClick={() => setShowAddPerson(true)}
            className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <UserPlus size={16} />
            Add Your First Person
          </button>
        </div>
      ) : (
        <div className="grid gap-3">
          {people.map((person) => (
            <div
              key={person.userId}
              onClick={() => setSelectedPersonId(person.userId)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setSelectedPersonId(person.userId);
                }
              }}
              tabIndex={0}
              role="button"
              aria-label={`View details for ${person.user.displayName}`}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Users size={18} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">{person.user.displayName}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{person.user.email}</p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-gray-500 dark:text-gray-400">Net:</span>
                      <span className={`font-semibold ${getBalanceColor(person.netBalance)}`}>
                        {getBalanceText(person.netBalance)}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Owes: {formatCurrency(person.totalOwed)} • Owing: {formatCurrency(person.totalOwing)}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {person.expenseCount} expense{person.expenseCount !== 1 ? 's' : ''}
                    </div>
                  </div>
                  <ArrowRight size={16} className="text-gray-400" />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Recent Expenses */}
      {recentExpenses.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-semibold text-gray-900 dark:text-white">Recent Expenses</h3>
            <button
              onClick={() => {
                // TODO: Navigate to all expenses view
                console.log('View all expenses');
              }}
              className="text-blue-600 dark:text-blue-400 text-sm font-medium hover:underline"
            >
              View All
            </button>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {recentExpenses.map((expense) => {
              const currentUserShare = expense.splitDetails?.[currentUser?.uid || ''] || 0;
              const isPaidByCurrentUser = expense.paidBy === currentUser?.uid;
              const otherUserIds = Object.keys(expense.splitDetails || {}).filter(id => id !== currentUser?.uid);
              const otherUser = otherUserIds.length > 0 ? users.find(u => u.uid === otherUserIds[0]) : null;

              return (
                <div
                  key={expense.id}
                  onClick={() => setSelectedExpense(expense)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      setSelectedExpense(expense);
                    }
                  }}
                  tabIndex={0}
                  role="button"
                  aria-label={`View details for expense: ${expense.title}`}
                  className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                        {getCategoryIcon(expense.category)}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white text-sm">{expense.title}</h4>
                        <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                          <Calendar size={10} />
                          <span>{expense.date.toLocaleDateString()}</span>
                          {otherUser && (
                            <>
                              <span>•</span>
                              <span>with {otherUser.displayName}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="font-semibold text-gray-900 dark:text-white text-sm">
                        {formatCurrency(expense.amount)}
                      </div>
                      <div className={`text-xs ${
                        isPaidByCurrentUser
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {isPaidByCurrentUser
                          ? `You paid ${formatCurrency(currentUserShare)}`
                          : `You owe ${formatCurrency(currentUserShare)}`
                        }
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}



      {/* Add Person Modal */}
      {showAddPerson && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-sm border border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
              <h2 className="font-semibold text-gray-900 dark:text-white">Add Person</h2>
              <button
                onClick={() => {
                  setShowAddPerson(false);
                  setSearchQuery('');
                  setSearchResults([]);
                }}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                <X size={16} />
              </button>
            </div>
            <div className="p-3 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Search existing users
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full px-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder="Search by name or email..."
                />
              </div>

              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded">
                  {searchResults.map((user) => (
                    <button
                      key={user.uid}
                      onClick={() => {
                        setSelectedPersonId(user.uid);
                        setShowAddPerson(false);
                        setSearchQuery('');
                        setSearchResults([]);
                      }}
                      className="w-full p-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                    >
                      <div className="font-medium text-sm text-gray-900 dark:text-white">
                        {user.displayName}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {user.email}
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {searchQuery && searchResults.length === 0 && (
                <div className="text-center py-3 text-sm text-gray-500 dark:text-gray-400">
                  No users found. They need to sign up first.
                </div>
              )}

              <div className="flex gap-2 pt-2">
                <button
                  onClick={() => {
                    setShowAddPerson(false);
                    setSearchQuery('');
                    setSearchResults([]);
                  }}
                  className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-1.5 px-3 rounded font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Person Detail Modal */}
      {selectedPerson && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
              <h2 className="font-semibold text-gray-900 dark:text-white">{selectedPerson.user.displayName}</h2>
              <button
                onClick={() => setSelectedPersonId(null)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                ×
              </button>
            </div>
            <div className="p-3">
              <div className="text-center mb-4">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(Math.abs(selectedPerson.netBalance))}
                </div>
                <div className={`text-sm ${getBalanceColor(selectedPerson.netBalance)}`}>
                  {getBalanceText(selectedPerson.netBalance)}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {selectedPerson.expenseCount} shared expense{selectedPerson.expenseCount !== 1 ? 's' : ''}
                </div>
              </div>

              <button
                onClick={() => {
                  // TODO: Navigate to add expense page with pre-selected person
                  console.log('Add expense with:', selectedPerson.user.displayName);
                }}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors mb-3"
              >
                Add Expense with {selectedPerson.user.displayName}
              </button>

              {/* Show shared expenses */}
              {getExpensesWithPerson(selectedPerson.userId).length > 0 ? (
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 dark:text-white text-sm">Shared Expenses</h4>
                  {getExpensesWithPerson(selectedPerson.userId).slice(0, 3).map((expense) => (
                    <div key={expense.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium text-sm text-gray-900 dark:text-white">{expense.title}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {expense.date.toLocaleDateString()}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatCurrency(expense.amount)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Your share: {formatCurrency(expense.splitDetails?.[currentUser?.uid || ''] || 0)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {getExpensesWithPerson(selectedPerson.userId).length > 3 && (
                    <div className="text-center">
                      <button className="text-blue-600 dark:text-blue-400 text-sm hover:underline">
                        View all {getExpensesWithPerson(selectedPerson.userId).length} expenses
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                  No shared expenses yet
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Expense Details Modal */}
      {selectedExpense && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md border border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
              <h2 className="font-semibold text-gray-900 dark:text-white">Expense Details</h2>
              <button
                onClick={() => setSelectedExpense(null)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                ×
              </button>
            </div>
            <div className="p-3 space-y-3">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(selectedExpense.amount)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {selectedExpense.title}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {selectedExpense.date.toLocaleDateString()} • {selectedExpense.category}
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Split Details</h4>
                <div className="space-y-1">
                  {Object.entries(selectedExpense.splitDetails || {}).map(([userId, amount]) => {
                    const user = userId === currentUser?.uid ?
                      { displayName: 'You', email: currentUser?.email } :
                      users.find(u => u.uid === userId);
                    const isPayer = selectedExpense.paidBy === userId;

                    return (
                      <div key={userId} className="flex justify-between items-center text-sm">
                        <span className="text-gray-700 dark:text-gray-300">
                          {user?.displayName || 'Unknown User'}
                          {isPayer && ' (paid)'}
                        </span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {formatCurrency(amount)}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {selectedExpense.description && (
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1 text-sm">Description</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedExpense.description}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Add Person Modal */}
      {showAddPerson && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-sm border border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
              <h2 className="font-semibold text-gray-900 dark:text-white">Add Person</h2>
              <button
                onClick={() => {
                  setShowAddPerson(false);
                  setSearchQuery('');
                  setSearchResults([]);
                }}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                <X size={16} />
              </button>
            </div>
            <div className="p-3 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Search existing users
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full px-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder="Search by name or email..."
                />
              </div>

              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded">
                  {searchResults.map((user) => (
                    <button
                      key={user.uid}
                      onClick={() => {
                        setSelectedPersonId(user.uid);
                        setShowAddPerson(false);
                        setSearchQuery('');
                        setSearchResults([]);
                      }}
                      className="w-full p-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                    >
                      <div className="font-medium text-sm text-gray-900 dark:text-white">
                        {user.displayName}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {user.email}
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {searchQuery && searchResults.length === 0 && (
                <div className="text-center py-3 text-sm text-gray-500 dark:text-gray-400">
                  No users found. They need to sign up first.
                </div>
              )}

              <div className="flex gap-2 pt-2">
                <button
                  onClick={() => {
                    setShowAddPerson(false);
                    setSearchQuery('');
                    setSearchResults([]);
                  }}
                  className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-1.5 px-3 rounded font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Person Detail Modal */}
      {selectedPerson && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
              <h2 className="font-semibold text-gray-900 dark:text-white">{selectedPerson.user.displayName}</h2>
              <button
                onClick={() => setSelectedPersonId(null)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                <X size={16} />
              </button>
            </div>
            <div className="p-3">
              <div className="text-center mb-4">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(Math.abs(selectedPerson.netBalance))}
                </div>
                <div className={`text-sm ${getBalanceColor(selectedPerson.netBalance)}`}>
                  {getBalanceText(selectedPerson.netBalance)}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {selectedPerson.expenseCount} shared expense{selectedPerson.expenseCount !== 1 ? 's' : ''}
                </div>
              </div>

              <button
                onClick={() => {
                  console.log('Add expense with:', selectedPerson.user.displayName);
                }}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors mb-3"
              >
                Add Expense with {selectedPerson.user.displayName}
              </button>

              {/* Show shared expenses */}
              {getExpensesWithPerson(selectedPerson.userId).length > 0 ? (
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 dark:text-white text-sm">Shared Expenses</h4>
                  {getExpensesWithPerson(selectedPerson.userId).slice(0, 3).map((expense) => (
                    <div key={expense.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium text-sm text-gray-900 dark:text-white">{expense.title}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {expense.date.toLocaleDateString()}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatCurrency(expense.amount)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Your share: {formatCurrency(expense.splitDetails?.[currentUser?.uid || ''] || 0)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {getExpensesWithPerson(selectedPerson.userId).length > 3 && (
                    <div className="text-center">
                      <button className="text-blue-600 dark:text-blue-400 text-sm hover:underline">
                        View all {getExpensesWithPerson(selectedPerson.userId).length} expenses
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                  No shared expenses yet
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Expense Details Modal */}
      {selectedExpense && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md border border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
              <h2 className="font-semibold text-gray-900 dark:text-white">Expense Details</h2>
              <button
                onClick={() => setSelectedExpense(null)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                <X size={16} />
              </button>
            </div>
            <div className="p-3 space-y-3">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(selectedExpense.amount)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {selectedExpense.title}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {selectedExpense.date.toLocaleDateString()} • {selectedExpense.category}
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Split Details</h4>
                <div className="space-y-1">
                  {Object.entries(selectedExpense.splitDetails || {}).map(([userId, amount]) => {
                    const user = userId === currentUser?.uid ?
                      { displayName: 'You', email: currentUser?.email } :
                      users.find(u => u.uid === userId);
                    const isPayer = selectedExpense.paidBy === userId;

                    return (
                      <div key={userId} className="flex justify-between items-center text-sm">
                        <span className="text-gray-700 dark:text-gray-300">
                          {user?.displayName || 'Unknown User'}
                          {isPayer && ' (paid)'}
                        </span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {formatCurrency(amount)}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {selectedExpense.description && (
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1 text-sm">Description</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedExpense.description}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PeopleExpenses;
