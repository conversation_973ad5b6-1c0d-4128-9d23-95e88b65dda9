import { doc, setDoc, getDoc, collection, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import { COLLECTIONS } from '../services/firestore';

export const testFirestoreConnection = async () => {
  try {
    console.log('Testing Firestore connection with splitwise collections...');

    // Test writing to splitwise-users collection
    const testRef = doc(db, COLLECTIONS.USERS, 'test-connection');
    await setDoc(testRef, {
      email: '<EMAIL>',
      displayName: 'Test User',
      isAdmin: false,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    console.log('✅ Successfully wrote to splitwise-users collection');

    // Try to read the document back
    const docSnap = await getDoc(testRef);
    if (docSnap.exists()) {
      console.log('✅ Successfully read from splitwise-users:', docSnap.data());
      return true;
    } else {
      console.log('❌ Document does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ Firestore connection failed:', error);
    return false;
  }
};

export const checkUserCollections = async () => {
  try {
    console.log('🔍 Checking splitwise-users collection...');

    // Check new splitwise-users collection only
    const newUsersRef = collection(db, COLLECTIONS.USERS);
    const newUsersSnap = await getDocs(newUsersRef);
    console.log(`📊 'splitwise-users' collection has ${newUsersSnap.size} documents`);
    newUsersSnap.forEach(doc => {
      if (doc.id !== 'test-connection') {
        console.log('User:', doc.id, doc.data());
      }
    });

  } catch (error) {
    console.error('❌ Error checking collections:', error);
  }
};

export const testUserCreation = async () => {
  try {
    console.log('🧪 Testing user creation in splitwise-users...');

    const testUserRef = doc(db, COLLECTIONS.USERS, 'test-user-creation');
    const testUserData = {
      email: '<EMAIL>',
      displayName: 'Test User',
      isAdmin: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('🧪 Attempting to write test user...');
    await setDoc(testUserRef, testUserData);
    console.log('✅ Test user creation successful!');

    // Try to read it back
    const readBack = await getDoc(testUserRef);
    if (readBack.exists()) {
      console.log('✅ Test user read back successful:', readBack.data());
    }

    return true;
  } catch (error) {
    console.error('❌ Test user creation failed:', error);
    return false;
  }
};

export const createTestUsers = async () => {
  try {
    console.log('👥 Creating test users for group testing...');

    const testUsers = [
      {
        uid: 'test-user-1',
        email: '<EMAIL>',
        displayName: 'John Doe',
        isAdmin: false
      },
      {
        uid: 'test-user-2',
        email: '<EMAIL>',
        displayName: 'Jane Smith',
        isAdmin: false
      },
      {
        uid: 'test-user-3',
        email: '<EMAIL>',
        displayName: 'Mike Johnson',
        isAdmin: false
      }
    ];

    for (const user of testUsers) {
      const userRef = doc(db, COLLECTIONS.USERS, user.uid);
      await setDoc(userRef, {
        ...user,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('✅ Created test user:', user.displayName, user.email);
    }

    console.log('✅ All test users created successfully!');
    return true;
  } catch (error) {
    console.error('❌ Test user creation failed:', error);
    return false;
  }
};

// Test functions available for manual testing
// Call testFirestoreConnection(), checkUserCollections(), or testUserCreation() manually in console if needed

// Make functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testUserCreation = testUserCreation;
  (window as any).testFirestoreConnection = testFirestoreConnection;
  (window as any).checkUserCollections = checkUserCollections;
  (window as any).createTestUsers = createTestUsers;
}
