import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Users, Plus, Settings, MoreVertical, Receipt, Calendar, User, DollarSign, Trash2, Tag } from 'lucide-react';
import { useGroups } from '../../hooks/useGroups';
import { useGroupExpenses } from '../../hooks/useExpenses';
import { useGroupBalances } from '../../hooks/useBalances';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../UI/LoadingSpinner';
import AddExpenseModal from './AddExpenseModal';
import SettleUpModal from './SettleUpModal';
import GroupExpenseDetails from './GroupExpenseDetails';
import { userService, groupService } from '../../services/firestore';
import { getCategoryIcon } from '../../services/expenseService';
import type { Group, Expense, User as UserType } from '../../types';

const GroupDetails: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { groups, loading: groupsLoading } = useGroups();
  const [group, setGroup] = useState<Group | null>(null);
  const { expenses: groupExpenses, loading: expensesLoading, addExpense } = useGroupExpenses(groupId || null);
  const { balances, loading: balancesLoading, settleUp, canDeleteGroup, getSimplifiedDebts } = useGroupBalances(groupId || null);
  const [showAddExpense, setShowAddExpense] = useState(false);
  const [showSettleUp, setShowSettleUp] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null);
  const [users, setUsers] = useState<Record<string, UserType>>({});

  useEffect(() => {
    if (groupId && groups.length > 0) {
      const foundGroup = groups.find(g => g.id === groupId);
      setGroup(foundGroup || null);
    }
  }, [groupId, groups]);

  // Fetch user data for all group members
  useEffect(() => {
    const fetchUsers = async () => {
      if (!group) return;

      try {
        const userPromises = group.members.map(userId => userService.get(userId));
        const userData = await Promise.all(userPromises);
        const usersMap: Record<string, UserType> = {};

        userData.forEach(user => {
          if (user) {
            usersMap[user.uid] = user;
          }
        });

        setUsers(usersMap);
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUsers();
  }, [group]);

  if (groupsLoading || expensesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size={48} text="Loading group details..." />
      </div>
    );
  }

  if (!group) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Group Not Found</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">The group you're looking for doesn't exist or you don't have access to it.</p>
            <button
              onClick={() => navigate('/groups')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              Back to Groups
            </button>
          </div>
        </div>
      </div>
    );
  }

  const isCreator = group.createdBy === currentUser?.uid;
  const isMember = group.members.includes(currentUser?.uid || '');

  const handleAddExpense = async (expenseData: any) => {
    try {
      await addExpense(expenseData);
    } catch (error) {
      console.error('Failed to add expense:', error);
      throw error;
    }
  };

  const handleDeleteGroup = async () => {
    if (!canDeleteGroup()) {
      alert('Cannot delete group with outstanding balances. Please settle all debts first.');
      return;
    }

    try {
      await groupService.delete(group.id, currentUser?.uid || '');
      navigate('/groups');
    } catch (error) {
      console.error('Failed to delete group:', error);
      alert('Failed to delete group. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-4xl mx-auto px-3 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={() => navigate('/groups')}
                className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <ArrowLeft size={18} className="text-gray-600 dark:text-gray-300" />
              </button>
              <div className="min-w-0">
                <h1 className="text-lg font-bold text-gray-900 dark:text-white truncate">{group.name}</h1>
                {group.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-300 truncate">{group.description}</p>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowAddExpense(true)}
                className="flex items-center gap-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
              >
                <Plus size={16} />
                Add Expense
              </button>
              {isCreator && (
                <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                  <Settings size={16} className="text-gray-600 dark:text-gray-300" />
                </button>
              )}
              <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                <MoreVertical size={16} className="text-gray-600 dark:text-gray-300" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-3 pb-20">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-4">
            {/* Group Stats */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <h2 className="text-base font-semibold text-gray-900 dark:text-white mb-3">Summary</h2>
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-xl font-bold text-green-600 dark:text-green-400">
                    ₹{group.totalExpenses.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">Total Expenses</div>
                </div>
                <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
                    {groupExpenses.length}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">Transactions</div>
                </div>
              </div>
            </div>

            {/* Recent Expenses */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-base font-semibold text-gray-900 dark:text-white">Recent Expenses</h2>
                <button className="text-blue-600 dark:text-blue-400 hover:underline text-xs">
                  View All
                </button>
              </div>
              
              {groupExpenses.length === 0 ? (
                <div className="text-center py-6">
                  <Receipt size={32} className="mx-auto text-gray-400 mb-3" />
                  <h3 className="text-base font-medium text-gray-900 dark:text-white mb-2">No Expenses Yet</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                    Start by adding your first group expense
                  </p>
                  <button
                    onClick={() => setShowAddExpense(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                  >
                    Add First Expense
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  {groupExpenses.slice(0, 5).map((expense) => {
                    const paidByUser = users[expense.paidBy];
                    const currentUserShare = expense.splitDetails?.[currentUser?.uid || ''] || 0;
                    const isPaidByCurrentUser = expense.paidBy === currentUser?.uid;

                    return (
                      <div
                        key={expense.id}
                        onClick={() => setSelectedExpense(expense)}
                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                            <span className="text-lg">{getCategoryIcon(expense.category)}</span>
                          </div>
                          <div className="min-w-0 flex-1">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">{expense.title}</h4>
                            <div className="flex items-center gap-3 text-xs text-gray-600 dark:text-gray-400">
                              <div className="flex items-center gap-1">
                                <Calendar size={10} />
                                <span>{expense.date.toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <User size={10} />
                                <span>Paid by {isPaidByCurrentUser ? 'You' : paidByUser?.displayName || 'Unknown'}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-semibold text-gray-900 dark:text-white">
                            ₹{expense.amount.toLocaleString()}
                          </div>
                          <div className="text-xs">
                            {isPaidByCurrentUser ? (
                              currentUserShare < expense.amount ? (
                                <span className="text-green-600 dark:text-green-400">
                                  You lent ₹{(expense.amount - currentUserShare).toLocaleString()}
                                </span>
                              ) : (
                                <span className="text-gray-600 dark:text-gray-400">You paid</span>
                              )
                            ) : currentUserShare > 0 ? (
                              <span className="text-red-600 dark:text-red-400">
                                You owe ₹{currentUserShare.toLocaleString()}
                              </span>
                            ) : (
                              <span className="text-gray-600 dark:text-gray-400">Not involved</span>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Balances */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-base font-semibold text-gray-900 dark:text-white">Balances</h2>
                <button
                  onClick={() => setShowSettleUp(true)}
                  className="flex items-center gap-1 bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs transition-colors"
                >
                  <DollarSign size={12} />
                  Settle Up
                </button>
              </div>

              {balancesLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                </div>
              ) : (
                <div className="space-y-3">
                  {/* Net Balances */}
                  <div className="space-y-1">
                    {Object.entries(balances).map(([userId, balance]) => {
                      const user = users[userId];
                      const userName = userId === currentUser?.uid ? 'You' : (user?.displayName || user?.email || `User ${userId.slice(0, 6)}`);
                      const netBalance = balance.netBalance;

                      return (
                        <div key={userId} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <div className="flex items-center gap-2">
                            <div className="w-5 h-5 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                              <User size={10} className="text-gray-600 dark:text-gray-300" />
                            </div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {userName}
                              {userId === group.createdBy && (
                                <span className="ml-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 px-1 rounded">
                                  Creator
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`text-sm font-semibold ${
                              netBalance > 0
                                ? 'text-green-600 dark:text-green-400'
                                : netBalance < 0
                                  ? 'text-red-600 dark:text-red-400'
                                  : 'text-gray-600 dark:text-gray-300'
                            }`}>
                              {netBalance > 0 ? '+' : ''}₹{Math.abs(netBalance).toLocaleString()}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-300">
                              {netBalance > 0 ? 'gets back' : netBalance < 0 ? 'owes' : 'settled'}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Detailed Debts */}
                  {getSimplifiedDebts && getSimplifiedDebts().length > 0 && (
                    <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
                      <h3 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Who Owes What:</h3>
                      <div className="space-y-1">
                        {getSimplifiedDebts().map((debt, index) => {
                          const fromUser = users[debt.fromUserId];
                          const toUser = users[debt.toUserId];
                          const fromName = debt.fromUserId === currentUser?.uid ? 'You' : (fromUser?.displayName || 'Unknown');
                          const toName = debt.toUserId === currentUser?.uid ? 'you' : (toUser?.displayName || 'Unknown');

                          return (
                            <div key={index} className="flex items-center justify-between text-xs p-1.5 bg-red-50 dark:bg-red-900/20 rounded">
                              <span className="text-gray-700 dark:text-gray-300">
                                <span className="font-medium">{fromName}</span> owes <span className="font-medium">{toName}</span>
                              </span>
                              <span className="font-semibold text-red-600 dark:text-red-400">
                                ₹{debt.amount.toLocaleString()}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Group Members */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-base font-semibold text-gray-900 dark:text-white">Members</h2>
                <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                  <Users size={14} />
                  <span className="text-sm font-medium">{group.members.length}</span>
                </div>
              </div>

              <div className="space-y-2">
                {group.members.map((memberId) => {
                  const user = users[memberId];
                  const userName = memberId === currentUser?.uid ? 'You' : (user?.displayName || user?.email || `User ${memberId.slice(0, 6)}`);

                  return (
                    <div key={memberId} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <User size={12} className="text-gray-600 dark:text-gray-300" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {userName}
                          {memberId === group.createdBy && (
                            <span className="ml-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 px-1 py-0.5 rounded">
                              Creator
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-300">
                          {user?.email || 'Member'}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
              
              {isCreator && (
                <button className="w-full mt-3 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 py-2 px-3 rounded-lg text-xs font-medium hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors">
                  Add Members
                </button>
              )}
            </div>

            {/* Group Info */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <h2 className="text-base font-semibold text-gray-900 dark:text-white mb-3">Info</h2>
              <div className="space-y-2 text-xs">
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <Calendar size={14} />
                  <span>Created {group.createdAt.toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <User size={14} />
                  <span>By {group.createdBy === currentUser?.uid ? 'You' : 'Creator'}</span>
                </div>
              </div>

              {isCreator && (
                <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => setShowDeleteConfirm(true)}
                    disabled={!canDeleteGroup()}
                    className={`w-full flex items-center justify-center gap-2 py-2 px-3 rounded-lg text-xs font-medium transition-colors ${
                      canDeleteGroup()
                        ? 'bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/50'
                        : 'bg-gray-50 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                    }`}
                    title={!canDeleteGroup() ? 'Cannot delete group with outstanding balances' : 'Delete group'}
                  >
                    <Trash2 size={12} />
                    Delete Group
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add Expense Modal */}
      {showAddExpense && (
        <AddExpenseModal
          onClose={() => setShowAddExpense(false)}
          onAddExpense={handleAddExpense}
          groupMembers={group.members}
          currentUserId={currentUser?.uid || ''}
          groupId={group.id}
        />
      )}

      {/* Settle Up Modal */}
      {showSettleUp && (
        <SettleUpModal
          onClose={() => setShowSettleUp(false)}
          balances={balances}
          users={users}
          currentUserId={currentUser?.uid || ''}
          onSettleUp={settleUp}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                  <Trash2 size={20} className="text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Delete Group</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">This action cannot be undone</p>
                </div>
              </div>

              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Are you sure you want to delete "<strong>{group.name}</strong>"?
                All expenses and data will be permanently removed.
              </p>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteGroup}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg font-medium transition-colors"
                >
                  Delete Group
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Expense Details Modal */}
      {selectedExpense && group && (
        <GroupExpenseDetails
          expense={selectedExpense}
          members={Object.values(users)}
          currentUserId={currentUser?.uid || ''}
          onClose={() => setSelectedExpense(null)}
        />
      )}
    </div>
  );
};

export default GroupDetails;
