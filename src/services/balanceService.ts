import { collection, query, where, getDocs, doc, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../config/firebase';
import type { Expense, Balance, Settlement } from '../types';

const COLLECTIONS = {
  EXPENSES: 'splitwiseExpenses',
  SETTLEMENTS: 'splitwiseSettlements'
};

export const balanceService = {
  /**
   * Calculate balances for a group based on all expenses
   */
  async calculateGroupBalances(groupId: string): Promise<Record<string, Balance>> {
    // Get all expenses for the group
    const expensesRef = collection(db, COLLECTIONS.EXPENSES);
    const q = query(expensesRef, where('groupId', '==', groupId));
    const snapshot = await getDocs(q);
    
    const expenses = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date?.toDate() || new Date(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Expense[];

    // Get all settlements for the group
    const settlementsRef = collection(db, COLLECTIONS.SETTLEMENTS);
    const settlementsQuery = query(settlementsRef, where('groupId', '==', groupId));
    const settlementsSnapshot = await getDocs(settlementsQuery);
    
    const settlements = settlementsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      settledAt: doc.data().settledAt?.toDate() || new Date()
    })) as Settlement[];

    return this.calculateBalances(expenses, settlements);
  },

  /**
   * Calculate balances from expenses and settlements
   */
  calculateBalances(expenses: Expense[], settlements: Settlement[] = []): Record<string, Balance> {
    const balances: Record<string, Balance> = {};

    // Initialize balances for all users involved
    const allUsers = new Set<string>();
    expenses.forEach(expense => {
      allUsers.add(expense.paidBy);
      Object.keys(expense.splitAmounts).forEach(userId => allUsers.add(userId));
    });
    settlements.forEach(settlement => {
      allUsers.add(settlement.fromUserId);
      allUsers.add(settlement.toUserId);
    });

    allUsers.forEach(userId => {
      balances[userId] = {
        userId,
        owes: {},
        owedBy: {},
        netBalance: 0
      };
    });

    // Process expenses
    expenses.forEach(expense => {
      const paidBy = expense.paidBy;
      const totalAmount = expense.amount;
      
      Object.entries(expense.splitAmounts).forEach(([userId, amount]) => {
        if (userId !== paidBy && amount > 0) {
          // This user owes money to the person who paid
          if (!balances[userId].owes[paidBy]) {
            balances[userId].owes[paidBy] = 0;
          }
          if (!balances[paidBy].owedBy[userId]) {
            balances[paidBy].owedBy[userId] = 0;
          }
          
          balances[userId].owes[paidBy] += amount;
          balances[paidBy].owedBy[userId] += amount;
        }
      });
    });

    // Process settlements (subtract settled amounts)
    settlements.forEach(settlement => {
      const { fromUserId, toUserId, amount } = settlement;
      
      // Reduce the debt
      if (balances[fromUserId].owes[toUserId]) {
        balances[fromUserId].owes[toUserId] = Math.max(0, balances[fromUserId].owes[toUserId] - amount);
        if (balances[fromUserId].owes[toUserId] === 0) {
          delete balances[fromUserId].owes[toUserId];
        }
      }
      
      if (balances[toUserId].owedBy[fromUserId]) {
        balances[toUserId].owedBy[fromUserId] = Math.max(0, balances[toUserId].owedBy[fromUserId] - amount);
        if (balances[toUserId].owedBy[fromUserId] === 0) {
          delete balances[toUserId].owedBy[fromUserId];
        }
      }
    });

    // Calculate net balances
    Object.values(balances).forEach(balance => {
      const totalOwed = Object.values(balance.owedBy).reduce((sum, amount) => sum + amount, 0);
      const totalOwes = Object.values(balance.owes).reduce((sum, amount) => sum + amount, 0);
      balance.netBalance = totalOwed - totalOwes;
    });

    return balances;
  },

  /**
   * Settle up between two users
   */
  async settleUp(groupId: string, fromUserId: string, toUserId: string, amount: number, description?: string): Promise<string> {
    const settlementsRef = collection(db, COLLECTIONS.SETTLEMENTS);
    const docRef = await addDoc(settlementsRef, {
      groupId,
      fromUserId,
      toUserId,
      amount,
      description: description || `Settlement between users`,
      settledAt: serverTimestamp()
    });
    
    return docRef.id;
  },

  /**
   * Get simplified debts (who owes whom how much)
   */
  getSimplifiedDebts(balances: Record<string, Balance>): Array<{fromUserId: string, toUserId: string, amount: number}> {
    const debts: Array<{fromUserId: string, toUserId: string, amount: number}> = [];
    
    Object.values(balances).forEach(balance => {
      Object.entries(balance.owes).forEach(([toUserId, amount]) => {
        if (amount > 0.01) { // Ignore very small amounts
          debts.push({
            fromUserId: balance.userId,
            toUserId,
            amount: Math.round(amount * 100) / 100 // Round to 2 decimal places
          });
        }
      });
    });
    
    return debts;
  },

  /**
   * Check if group can be deleted (no outstanding balances)
   */
  canDeleteGroup(balances: Record<string, Balance>): boolean {
    return Object.values(balances).every(balance => 
      Math.abs(balance.netBalance) < 0.01 && 
      Object.values(balance.owes).every(amount => amount < 0.01)
    );
  },

  /**
   * Get total amount a user owes in a group
   */
  getTotalOwed(balance: Balance): number {
    return Object.values(balance.owes).reduce((sum, amount) => sum + amount, 0);
  },

  /**
   * Get total amount owed to a user in a group
   */
  getTotalOwedBy(balance: Balance): number {
    return Object.values(balance.owedBy).reduce((sum, amount) => sum + amount, 0);
  }
};
