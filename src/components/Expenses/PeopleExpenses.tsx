import React, { useState } from 'react';
import { Users, Plus, Receipt, Calculator, ArrowRight, Calendar, Tag } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../UI/LoadingSpinner';
import PersonExpenseDetails from './PersonExpenseDetails';
import { getCategoryIcon } from '../../services/expenseService';

interface Person {
  id: string;
  name: string;
  email?: string;
  totalOwed: number;
  totalOwing: number;
  netBalance: number;
}

interface PersonExpense {
  id: string;
  personId: string;
  title: string;
  amount: number;
  splitType: 'equal' | 'manual' | 'percentage' | 'shares';
  splitDetails: Record<string, number>;
  paidBy: string;
  date: Date;
  category: string;
  description?: string;
}

const PeopleExpenses: React.FC = () => {
  const { currentUser } = useAuth();
  const [showAddPerson, setShowAddPerson] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState<Person | null>(null);
  const [selectedExpense, setSelectedExpense] = useState<PersonExpense | null>(null);
  const [loading, setLoading] = useState(false);

  // Mock data for now - this will be replaced with real data
  const people: Person[] = [
    {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      totalOwed: 1500,
      totalOwing: 800,
      netBalance: 700
    },
    {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      totalOwed: 600,
      totalOwing: 1200,
      netBalance: -600
    },
    {
      id: '3',
      name: 'Mike Johnson',
      totalOwed: 300,
      totalOwing: 450,
      netBalance: -150
    }
  ];

  // Mock expense data
  const mockExpenses: PersonExpense[] = [
    {
      id: '1',
      personId: '1',
      title: 'Dinner at Restaurant',
      amount: 1200,
      splitType: 'equal',
      splitDetails: {
        [currentUser?.uid || 'current']: 600,
        '1': 600
      },
      paidBy: currentUser?.uid || 'current',
      date: new Date('2025-07-02'),
      category: 'food',
      description: 'Had dinner together at the new Italian place'
    },
    {
      id: '2',
      personId: '2',
      title: 'Movie Tickets',
      amount: 800,
      splitType: 'equal',
      splitDetails: {
        [currentUser?.uid || 'current']: 400,
        '2': 400
      },
      paidBy: '2',
      date: new Date('2025-07-01'),
      category: 'entertainment'
    },
    {
      id: '3',
      personId: '3',
      title: 'Grocery Shopping',
      amount: 1500,
      splitType: 'manual',
      splitDetails: {
        [currentUser?.uid || 'current']: 900,
        '3': 600
      },
      paidBy: currentUser?.uid || 'current',
      date: new Date('2025-06-30'),
      category: 'groceries',
      description: 'Weekly grocery shopping split'
    }
  ];

  if (loading) {
    return <LoadingSpinner text="Loading people..." />;
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">People & Expenses</h1>
          <p className="text-sm text-gray-600 dark:text-gray-400">Manage expenses with different people</p>
        </div>
        <button
          onClick={() => setShowAddPerson(true)}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          <Plus size={16} />
          Add Person
        </button>
      </div>

      {/* People List */}
      <div className="grid gap-3">
        {people.map((person) => (
          <div
            key={person.id}
            onClick={() => setSelectedPerson(person)}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow cursor-pointer"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Users size={18} className="text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">{person.name}</h3>
                  {person.email && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">{person.email}</p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="text-right">
                  <div className="flex items-center gap-2 text-sm">
                    <span className="text-gray-500 dark:text-gray-400">Net:</span>
                    <span className={`font-semibold ${
                      person.netBalance > 0 
                        ? 'text-green-600 dark:text-green-400' 
                        : person.netBalance < 0 
                        ? 'text-red-600 dark:text-red-400'
                        : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      ₹{Math.abs(person.netBalance)}
                      {person.netBalance > 0 && ' owed to you'}
                      {person.netBalance < 0 && ' you owe'}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Owes: ₹{person.totalOwed} • Owing: ₹{person.totalOwing}
                  </div>
                </div>
                <ArrowRight size={16} className="text-gray-400" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Expenses */}
      {mockExpenses.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-semibold text-gray-900 dark:text-white">Recent Expenses</h3>
            <button className="text-blue-600 dark:text-blue-400 text-sm font-medium hover:underline">
              View All
            </button>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {mockExpenses.slice(0, 5).map((expense) => {
              const person = people.find(p => p.id === expense.personId);
              const currentUserShare = expense.splitDetails[currentUser?.uid || 'current'] || 0;
              const isPaidByCurrentUser = expense.paidBy === (currentUser?.uid || 'current');

              return (
                <div
                  key={expense.id}
                  onClick={() => setSelectedExpense(expense)}
                  className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                        <span className="text-lg">{getCategoryIcon(expense.category)}</span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white truncate">{expense.title}</h4>
                        <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-1">
                            <Calendar size={12} />
                            <span>{expense.date.toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Users size={12} />
                            <span>with {person?.name || 'Unknown'}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="font-semibold text-gray-900 dark:text-white">
                        ₹{expense.amount.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {isPaidByCurrentUser ? (
                          currentUserShare < expense.amount ? (
                            <span className="text-green-600 dark:text-green-400">
                              You're owed ₹{(expense.amount - currentUserShare).toLocaleString()}
                            </span>
                          ) : (
                            <span className="text-gray-600 dark:text-gray-400">You paid</span>
                          )
                        ) : (
                          <span className="text-red-600 dark:text-red-400">
                            You owe ₹{currentUserShare.toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Empty State */}
      {people.length === 0 && (
        <div className="text-center py-12">
          <Receipt size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No people added yet</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Add people to start tracking shared expenses
          </p>
          <button
            onClick={() => setShowAddPerson(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            Add Your First Person
          </button>
        </div>
      )}

      {/* Add Person Modal */}
      {showAddPerson && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-sm border border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
              <h2 className="font-semibold text-gray-900 dark:text-white">Add Person</h2>
              <button
                onClick={() => setShowAddPerson(false)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                ×
              </button>
            </div>
            <div className="p-3 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Name *
                </label>
                <input
                  type="text"
                  className="w-full px-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder="Person's name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email (optional)
                </label>
                <input
                  type="email"
                  className="w-full px-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="flex gap-2 pt-2">
                <button
                  onClick={() => setShowAddPerson(false)}
                  className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-1.5 px-3 rounded font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
                >
                  Cancel
                </button>
                <button
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-1.5 px-3 rounded font-medium transition-colors text-sm"
                >
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Person Detail Modal */}
      {selectedPerson && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
              <h2 className="font-semibold text-gray-900 dark:text-white">{selectedPerson.name}</h2>
              <button
                onClick={() => setSelectedPerson(null)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                ×
              </button>
            </div>
            <div className="p-3">
              <div className="text-center mb-4">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  ₹{Math.abs(selectedPerson.netBalance)}
                </div>
                <div className={`text-sm ${
                  selectedPerson.netBalance > 0 
                    ? 'text-green-600 dark:text-green-400' 
                    : selectedPerson.netBalance < 0 
                    ? 'text-red-600 dark:text-red-400'
                    : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {selectedPerson.netBalance > 0 && 'owes you'}
                  {selectedPerson.netBalance < 0 && 'you owe'}
                  {selectedPerson.netBalance === 0 && 'settled up'}
                </div>
              </div>
              
              <button
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors mb-3"
              >
                Add Expense with {selectedPerson.name}
              </button>
              
              <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                No expenses yet
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Expense Details Modal */}
      {selectedExpense && (
        <PersonExpenseDetails
          expense={{
            ...selectedExpense,
            splitWith: Object.keys(selectedExpense.splitDetails)
          }}
          people={[
            ...people,
            {
              id: currentUser?.uid || 'current',
              name: 'You',
              email: currentUser?.email
            }
          ]}
          currentUserId={currentUser?.uid || 'current'}
          onClose={() => setSelectedExpense(null)}
        />
      )}
    </div>
  );
};

export default PeopleExpenses;
