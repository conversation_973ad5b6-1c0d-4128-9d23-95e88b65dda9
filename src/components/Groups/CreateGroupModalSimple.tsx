import React, { useState } from 'react';
import { X, Users, Plus, Trash2, Mail, UserPlus } from 'lucide-react';
import { useUsers } from '../../hooks/useUsers';
import type { Group } from '../../types';

interface CreateGroupModalProps {
  onClose: () => void;
  onCreateGroup: (groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  currentUserId: string;
}

const CreateGroupModal: React.FC<CreateGroupModalProps> = ({
  onClose,
  onCreateGroup,
  currentUserId
}) => {
  const { users, searchUsers } = useUsers();
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [memberEmails, setMemberEmails] = useState<string[]>(['']);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      setError('Group name is required');
      return;
    }

    if (!currentUserId) {
      setError('User not authenticated. Please refresh and try again.');
      return;
    }

    try {
      setLoading(true);
      setError('');

      console.log('🔄 Starting group creation process...');
      console.log('📋 Form data:', formData);
      console.log('👥 Member emails:', memberEmails);
      console.log('🆔 Current user ID:', currentUserId);

      // Filter valid emails
      const validEmails = memberEmails
        .filter(email => email.trim())
        .filter(email => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email));

      console.log('✅ Valid emails to process:', validEmails);
      console.log('📧 All member emails from form:', memberEmails);

      const groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'> = {
        name: formData.name.trim() || '',
        description: formData.description.trim() || '',
        members: [currentUserId], // Start with creator only
        memberEmails: validEmails.length > 0 ? validEmails : [], // Include emails for processing
        createdBy: currentUserId,
        totalExpenses: 0,
        isActive: true
      };

      console.log('📦 Final group data being sent:', groupData);

      console.log('📦 Group data to create:', groupData);

      const groupId = await onCreateGroup(groupData);
      console.log('✅ Group created successfully with ID:', groupId);
      onClose();
    } catch (err: any) {
      console.error('❌ Group creation failed:', err);
      console.error('❌ Error details:', {
        message: err?.message,
        code: err?.code,
        stack: err?.stack
      });
      setError(`Failed to create group: ${err?.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const addEmailField = () => {
    setMemberEmails([...memberEmails, '']);
  };

  const removeEmailField = (index: number) => {
    if (memberEmails.length > 1) {
      setMemberEmails(memberEmails.filter((_, i) => i !== index));
    }
  };

  const updateEmail = (index: number, email: string) => {
    const updated = [...memberEmails];
    updated[index] = email;
    setMemberEmails(updated);
  };

  const addUserFromSearch = (user: any) => {
    console.log('👤 Adding user from search:', user);
    console.log('📧 Current member emails:', memberEmails);

    if (!memberEmails.includes(user.email)) {
      const updatedEmails = [...memberEmails.filter(e => e.trim()), user.email, ''];
      console.log('📧 Updated member emails:', updatedEmails);
      setMemberEmails(updatedEmails);
      setSearchQuery('');
      setShowSearch(false);
    } else {
      console.log('⚠️ User email already in list:', user.email);
    }
  };

  const searchResults = searchQuery.trim() ? searchUsers(searchQuery).slice(0, 3) : [];

  // Debug: Log available users
  React.useEffect(() => {
    console.log('👥 Available users for search:', users);
    console.log('🔍 Current search query:', searchQuery);
    console.log('📋 Search results:', searchResults);
  }, [users, searchQuery, searchResults]);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <div className="w-7 h-7 bg-gradient-to-r from-blue-500 to-purple-600 rounded-md flex items-center justify-center">
              <Users size={14} className="text-white" />
            </div>
            <h2 className="font-semibold text-gray-900 dark:text-white">New Group</h2>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          >
            <X size={16} className="text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-3 space-y-3">
          {error && (
            <div className="p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 rounded text-sm">
              {error}
            </div>
          )}

          {/* Group Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Group Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
              placeholder="e.g., Trip to Goa"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description (optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-colors resize-none text-sm"
              placeholder="What's this group for?"
              rows={2}
            />
          </div>

          {/* Add Members */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Add Members
              </label>
              <button
                type="button"
                onClick={() => setShowSearch(!showSearch)}
                className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center gap-1"
              >
                <UserPlus size={12} />
                Search users
              </button>
            </div>

            {/* Search existing users */}
            {showSearch && (
              <div className="mb-2 relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
                  placeholder="Search by name or email..."
                />

                {searchResults.length > 0 && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded shadow-lg z-10 max-h-32 overflow-y-auto">
                    {searchResults.map((user) => (
                      <button
                        key={user.uid}
                        type="button"
                        onClick={() => addUserFromSearch(user)}
                        className="w-full text-left px-2 py-1.5 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0 transition-colors"
                      >
                        <div className="text-sm font-medium text-gray-900 dark:text-white">{user.displayName}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">{user.email}</div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Email inputs */}
            <div className="space-y-2">
              {memberEmails.map((email, index) => (
                <div key={index} className="flex gap-2">
                  <div className="flex-1 relative">
                    <Mail className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" size={12} />
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => updateEmail(index, e.target.value)}
                      className="w-full pl-6 pr-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  {memberEmails.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeEmailField(index)}
                      className="p-1.5 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                    >
                      <Trash2 size={12} />
                    </button>
                  )}
                </div>
              ))}

              <button
                type="button"
                onClick={addEmailField}
                className="w-full p-1.5 border border-dashed border-gray-300 dark:border-gray-600 rounded text-gray-500 dark:text-gray-400 hover:border-blue-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors flex items-center justify-center gap-1 text-sm"
              >
                <Plus size={12} />
                Add email
              </button>
            </div>
          </div>

          {/* Auto-included notice */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded p-2">
            <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300 text-xs">
              <Users size={12} />
              <span>You'll be added as the group creator</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-1">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-1.5 px-3 rounded font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white py-1.5 px-3 rounded font-medium transition-colors text-sm"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center justify-center gap-1">
                  <div className="w-3 h-3 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Creating...
                </div>
              ) : (
                'Create'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateGroupModal;
