import {
  collection,
  doc,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  limit,
  onSnapshot,
  serverTimestamp,
  writeBatch,
  increment,
  Timestamp,
  DocumentReference
} from 'firebase/firestore';
import { db } from '../config/firebase';
import type {
  User,
  Group,
  ActivityLog
} from '../types';

// Collections with splitwise prefix
export const COLLECTIONS = {
  USERS: 'splitwise-users',
  GROUPS: 'splitwise-groups',
  EXPENSES: 'splitwise-expenses',
  PERSONAL_EXPENSES: 'splitwise-personalExpenses',
  ACTIVITY_LOGS: 'splitwise-activityLogs',
  BALANCES: 'splitwise-balances',
  STATISTICS: 'splitwise-statistics'
} as const;

// User operations
export const userService = {
  async create(userData: Omit<User, 'uid' | 'createdAt' | 'updatedAt'> & { uid: string }) {
    const userRef = doc(db, COLLECTIONS.USERS, userData.uid);
    await setDoc(userRef, {
      ...userData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    return userData.uid;
  },

  async get(uid: string): Promise<User | null> {
    const userRef = doc(db, COLLECTIONS.USERS, uid);
    const userSnap = await getDoc(userRef);
    
    if (userSnap.exists()) {
      const data = userSnap.data();
      return {
        uid: userSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as User;
    }
    return null;
  },

  async update(uid: string, updates: Partial<Omit<User, 'uid' | 'createdAt'>>) {
    const userRef = doc(db, COLLECTIONS.USERS, uid);
    await updateDoc(userRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  },

  async getAll(): Promise<User[]> {
    const usersRef = collection(db, COLLECTIONS.USERS);
    const snapshot = await getDocs(usersRef);
    
    return snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as User[];
  }
};

// Group operations
export const groupService = {
  async create(groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      console.log('🔄 Starting group creation...', groupData);

      console.log('🔄 Processing members...');
      const groupsRef = collection(db, COLLECTIONS.GROUPS);

      // Start with the creator as a member
      let finalMembers = [groupData.createdBy];
      console.log('👤 Initial members (creator):', finalMembers);

      // Process member emails if provided
      if (groupData.memberEmails && groupData.memberEmails.length > 0) {
        console.log('📧 Processing member emails:', groupData.memberEmails);

        const usersRef = collection(db, COLLECTIONS.USERS);
        const addedUsers: string[] = [];

        for (const email of groupData.memberEmails) {
          if (!email.trim()) continue; // Skip empty emails

          console.log('🔍 Looking for user with email:', email);
          const q = query(usersRef, where('email', '==', email.trim()));
          const snapshot = await getDocs(q);

          if (!snapshot.empty) {
            const userData = snapshot.docs[0].data();
            const userId = userData.uid;
            console.log('✅ Found user:', { email, userId, name: userData.displayName });

            if (!finalMembers.includes(userId)) {
              finalMembers.push(userId);
              addedUsers.push(email);
              console.log('➕ Added user to group:', userId);
            } else {
              console.log('⚠️ User already in group:', userId);
            }
          } else {
            console.log('❌ No user found with email:', email);
          }
        }

        console.log(`✅ Successfully added ${addedUsers.length} existing users:`, addedUsers);
        console.log('👥 Final members list:', finalMembers);

        // Store remaining emails for future invitations
        const remainingEmails = groupData.memberEmails.filter(email =>
          email.trim() && !addedUsers.includes(email)
        );
        if (remainingEmails.length > 0) {
          console.log('📧 Emails for future invitations:', remainingEmails);
        }
      }

      console.log('🔄 Creating group document...');

      // Ensure all fields have proper values (no undefined)
      const groupDocData = {
        name: groupData.name || '',
        description: groupData.description || '',
        members: finalMembers || [groupData.createdBy],
        memberEmails: (groupData.memberEmails || []).filter(email => email && email.trim()),
        createdBy: groupData.createdBy,
        totalExpenses: groupData.totalExpenses || 0,
        isActive: groupData.isActive !== undefined ? groupData.isActive : true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      console.log('📦 Final document data (no undefined values):', groupDocData);

      const docRef = await addDoc(groupsRef, groupDocData);
      console.log('✅ Group document created with ID:', docRef.id);
      console.log('👥 Group created with members:', finalMembers);

      console.log('✅ Group creation completed successfully');
      return docRef.id;
    } catch (error) {
      console.error('❌ Group creation failed:', error);
      throw error;
    }
  },

  async delete(groupId: string, userId: string) {
    const groupRef = doc(db, COLLECTIONS.GROUPS, groupId);
    await deleteDoc(groupRef);

    // Log activity
    await activityService.log({
      userId,
      action: 'deleted_group' as any,
      entityType: 'group',
      entityId: groupId,
      details: `Deleted group`,
      metadata: {}
    });
  },

  async get(id: string): Promise<Group | null> {
    const groupRef = doc(db, COLLECTIONS.GROUPS, id);
    const groupSnap = await getDoc(groupRef);
    
    if (groupSnap.exists()) {
      const data = groupSnap.data();
      return {
        id: groupSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Group;
    }
    return null;
  },

  async getUserGroups(userId: string): Promise<Group[]> {
    const groupsRef = collection(db, COLLECTIONS.GROUPS);
    const q = query(
      groupsRef,
      where('members', 'array-contains', userId)
    );

    const snapshot = await getDocs(q);
    const groups = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Group[];

    // Filter and sort in memory to avoid index requirements
    return groups
      .filter(group => group.isActive)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  },

  async update(id: string, updates: Partial<Omit<Group, 'id' | 'createdAt'>>) {
    const groupRef = doc(db, COLLECTIONS.GROUPS, id);
    await updateDoc(groupRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  },

  async addMember(groupId: string, userId: string) {
    const groupRef = doc(db, COLLECTIONS.GROUPS, groupId);
    const group = await this.get(groupId);
    
    if (group && !group.members.includes(userId)) {
      await updateDoc(groupRef, {
        members: [...group.members, userId],
        updatedAt: serverTimestamp()
      });
    }
  },

  async removeMember(groupId: string, userId: string) {
    const groupRef = doc(db, COLLECTIONS.GROUPS, groupId);
    const group = await this.get(groupId);
    
    if (group) {
      await updateDoc(groupRef, {
        members: group.members.filter(id => id !== userId),
        updatedAt: serverTimestamp()
      });
    }
  },

  onSnapshot(userId: string, callback: (groups: Group[]) => void) {
    const groupsRef = collection(db, COLLECTIONS.GROUPS);
    const q = query(
      groupsRef,
      where('members', 'array-contains', userId)
    );

    return onSnapshot(q, (snapshot) => {
      const groups = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as Group[];

      // Filter and sort in memory to avoid index requirements
      const filteredGroups = groups
        .filter(group => group.isActive)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());

      callback(filteredGroups);
    });
  }
};

// Activity logging
export const activityService = {
  async log(activity: Omit<ActivityLog, 'id' | 'timestamp'>) {
    try {
      console.log('🔄 Logging activity:', activity);
      const logsRef = collection(db, COLLECTIONS.ACTIVITY_LOGS);
      await addDoc(logsRef, {
        ...activity,
        timestamp: serverTimestamp()
      });
      console.log('✅ Activity logged successfully');
    } catch (error) {
      console.error('❌ Activity logging failed:', error);
      throw error;
    }
  },

  async getUserActivity(userId: string, limitCount = 50): Promise<ActivityLog[]> {
    const logsRef = collection(db, COLLECTIONS.ACTIVITY_LOGS);
    const q = query(
      logsRef,
      where('userId', '==', userId),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);
    const activities = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate() || new Date()
    })) as ActivityLog[];

    // Sort in memory to avoid index requirements
    return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  },

  onSnapshot(userId: string, callback: (activities: ActivityLog[]) => void) {
    const logsRef = collection(db, COLLECTIONS.ACTIVITY_LOGS);
    const q = query(
      logsRef,
      where('userId', '==', userId),
      limit(50)
    );

    return onSnapshot(q, (snapshot) => {
      const activities = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      })) as ActivityLog[];

      // Sort in memory to avoid index requirements
      const sortedActivities = activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      callback(sortedActivities);
    });
  }
};

// Utility functions
export const firestoreUtils = {
  async batchWrite(operations: Array<{ type: 'set' | 'update' | 'delete', ref: DocumentReference, data?: object }>) {
    const batch = writeBatch(db);
    
    operations.forEach(({ type, ref, data }) => {
      switch (type) {
        case 'set':
          if (data) batch.set(ref, data);
          break;
        case 'update':
          if (data) batch.update(ref, data);
          break;
        case 'delete':
          batch.delete(ref);
          break;
      }
    });
    
    await batch.commit();
  },

  serverTimestamp,
  increment,
  
  // Convert Firestore timestamp to Date
  timestampToDate(timestamp: Timestamp | null): Date {
    return timestamp ? timestamp.toDate() : new Date();
  }
};
