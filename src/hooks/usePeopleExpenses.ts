import { useState, useEffect } from 'react';
import { userService } from '../services/firestore';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import { useAuth } from '../contexts/AuthContext';
import type { User, Expense } from '../types';

interface PersonBalance {
  userId: string;
  user: User;
  totalOwed: number;    // How much this person owes to current user
  totalOwing: number;   // How much current user owes to this person
  netBalance: number;   // Positive = they owe you, Negative = you owe them
  expenseCount: number;
}

export const usePeopleExpenses = () => {
  const { currentUser } = useAuth();
  const [people, setPeople] = useState<PersonBalance[]>([]);
  const [allExpenses, setAllExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser?.uid) return;

    const fetchPeopleAndExpenses = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get all users from splitwise-users collection
        const allUsers = await userService.getAll();

        // Get ALL expenses from splitwise-expenses collection (no complex queries)
        const expensesRef = collection(db, 'splitwise-expenses');
        const expensesSnapshot = await getDocs(expensesRef);

        const allExpensesData = expensesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          date: doc.data().date?.toDate() || new Date(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date()
        })) as Expense[];

        // Filter expenses that involve current user (in memory)
        const userExpenses = allExpensesData.filter(expense =>
          expense.splitBetween?.includes(currentUser.uid) ||
          expense.paidBy === currentUser.uid ||
          (expense.splitDetails && Object.keys(expense.splitDetails).includes(currentUser.uid))
        );

        setAllExpenses(userExpenses);

        // Calculate balances for each person
        const balanceMap = new Map<string, {
          user: User;
          totalOwed: number;
          totalOwing: number;
          expenseCount: number;
        }>();

        // Initialize all users (except current user)
        allUsers.forEach(user => {
          if (user.uid !== currentUser.uid) {
            balanceMap.set(user.uid, {
              user,
              totalOwed: 0,
              totalOwing: 0,
              expenseCount: 0
            });
          }
        });

        // Calculate balances from expenses
        userExpenses.forEach(expense => {
          const currentUserShare = expense.splitDetails?.[currentUser.uid] || 0;
          const isPaidByCurrentUser = expense.paidBy === currentUser.uid;

          // Process each person in the split
          Object.entries(expense.splitDetails || {}).forEach(([userId, amount]) => {
            if (userId === currentUser.uid) return; // Skip current user

            const personBalance = balanceMap.get(userId);
            if (!personBalance) return;

            personBalance.expenseCount++;

            if (isPaidByCurrentUser) {
              // Current user paid, so this person owes current user
              personBalance.totalOwed += amount;
            } else if (expense.paidBy === userId) {
              // This person paid, so current user owes them
              personBalance.totalOwing += currentUserShare;
            }
          });
        });

        // Convert to array and calculate net balances
        const peopleBalances: PersonBalance[] = Array.from(balanceMap.values())
          .filter(balance => balance.expenseCount > 0) // Only show people with shared expenses
          .map(balance => ({
            userId: balance.user.uid,
            user: balance.user,
            totalOwed: balance.totalOwed,
            totalOwing: balance.totalOwing,
            netBalance: balance.totalOwed - balance.totalOwing,
            expenseCount: balance.expenseCount
          }))
          .sort((a, b) => Math.abs(b.netBalance) - Math.abs(a.netBalance)); // Sort by absolute balance

        setPeople(peopleBalances);
      } catch (err) {
        console.error('Error fetching people and expenses:', err);
        setError('Failed to load people and expenses');
      } finally {
        setLoading(false);
      }
    };

    fetchPeopleAndExpenses();
  }, [currentUser?.uid]);

  // Get expenses between current user and a specific person
  const getExpensesWithPerson = (personId: string): Expense[] => {
    return allExpenses.filter(expense => {
      const splitUserIds = Object.keys(expense.splitDetails || {});
      return splitUserIds.includes(currentUser?.uid || '') && splitUserIds.includes(personId);
    });
  };

  // Get recent expenses across all people
  const getRecentExpenses = (limit: number = 10): Expense[] => {
    return allExpenses
      .slice(0, limit)
      .sort((a, b) => b.date.getTime() - a.date.getTime());
  };

  return {
    people,
    allExpenses,
    loading,
    error,
    getExpensesWithPerson,
    getRecentExpenses
  };
};
