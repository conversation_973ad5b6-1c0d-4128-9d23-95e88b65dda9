import React, { useState } from 'react';
import { X, DollarSign, User } from 'lucide-react';
import type { Balance, User as UserType } from '../../types';

interface SettleUpModalProps {
  onClose: () => void;
  balances: Record<string, Balance>;
  users: Record<string, UserType>;
  currentUserId: string;
  onSettleUp: (fromUserId: string, toUserId: string, amount: number, description?: string) => Promise<void>;
}

const SettleUpModal: React.FC<SettleUpModalProps> = ({
  onClose,
  balances,
  users,
  currentUserId,
  onSettleUp
}) => {
  const [selectedFromUser, setSelectedFromUser] = useState('');
  const [selectedToUser, setSelectedToUser] = useState('');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Get users who owe money (negative balance)
  const debtors = Object.values(balances).filter(balance => balance.netBalance < 0);
  
  // Get users who are owed money (positive balance)
  const creditors = Object.values(balances).filter(balance => balance.netBalance > 0);

  // Get suggested settlements
  const getSuggestedSettlements = () => {
    const suggestions: Array<{fromUserId: string, toUserId: string, amount: number}> = [];
    
    Object.values(balances).forEach(balance => {
      Object.entries(balance.owes).forEach(([toUserId, amount]) => {
        if (amount > 0.01) {
          suggestions.push({
            fromUserId: balance.userId,
            toUserId,
            amount: Math.round(amount * 100) / 100
          });
        }
      });
    });
    
    return suggestions.sort((a, b) => b.amount - a.amount);
  };

  const suggestedSettlements = getSuggestedSettlements();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFromUser || !selectedToUser || !amount || parseFloat(amount) <= 0) {
      setError('Please fill in all fields with valid amounts');
      return;
    }

    if (selectedFromUser === selectedToUser) {
      setError('Cannot settle with yourself');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      await onSettleUp(
        selectedFromUser,
        selectedToUser,
        parseFloat(amount),
        description.trim() || undefined
      );
      
      onClose();
    } catch (err) {
      setError('Failed to settle up. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSuggestedSettlement = (suggestion: {fromUserId: string, toUserId: string, amount: number}) => {
    setSelectedFromUser(suggestion.fromUserId);
    setSelectedToUser(suggestion.toUserId);
    setAmount(suggestion.amount.toString());
    setDescription(`Settlement for group expenses`);
  };

  const getUserName = (userId: string) => {
    if (userId === currentUserId) return 'You';
    const user = users[userId];
    return user?.displayName || user?.email || `User ${userId.slice(0, 6)}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Settle Up</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X size={18} className="text-gray-600 dark:text-gray-300" />
          </button>
        </div>

        <div className="p-4 space-y-4">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-3">
              <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Suggested Settlements */}
          {suggestedSettlements.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Suggested Settlements</h3>
              <div className="space-y-2">
                {suggestedSettlements.slice(0, 3).map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestedSettlement(suggestion)}
                    className="w-full p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg text-left hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="text-sm">
                        <span className="font-medium text-gray-900 dark:text-white">
                          {getUserName(suggestion.fromUserId)}
                        </span>
                        <span className="text-gray-600 dark:text-gray-300"> pays </span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {getUserName(suggestion.toUserId)}
                        </span>
                      </div>
                      <div className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                        ₹{suggestion.amount.toLocaleString()}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Manual Settlement Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Who is paying?
              </label>
              <select
                value={selectedFromUser}
                onChange={(e) => setSelectedFromUser(e.target.value)}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select person paying</option>
                {Object.keys(balances).map(userId => (
                  <option key={userId} value={userId}>
                    {getUserName(userId)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Who is receiving?
              </label>
              <select
                value={selectedToUser}
                onChange={(e) => setSelectedToUser(e.target.value)}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select person receiving</option>
                {Object.keys(balances).map(userId => (
                  <option key={userId} value={userId}>
                    {getUserName(userId)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Amount (₹)
              </label>
              <input
                type="number"
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0.00"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description (optional)
              </label>
              <input
                type="text"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Settlement description..."
              />
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white py-3 px-4 rounded-lg font-medium transition-colors"
                disabled={loading}
              >
                {loading ? 'Settling...' : 'Settle Up'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SettleUpModal;
