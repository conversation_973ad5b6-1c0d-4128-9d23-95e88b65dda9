import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  writeBatch,
  increment
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { COLLECTIONS, activityService } from './firestore';
import type { Expense, PersonalExpense, ExpenseCategory } from '../types';

export const expenseService = {
  // Group expenses
  async createGroupExpense(expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) {
    const batch = writeBatch(db);
    
    // Add expense
    const expensesRef = collection(db, COLLECTIONS.EXPENSES);
    const expenseRef = doc(expensesRef);
    
    batch.set(expenseRef, {
      ...expenseData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    // Update group total
    if (expenseData.groupId) {
      const groupRef = doc(db, COLLECTIONS.GROUPS, expenseData.groupId);
      batch.update(groupRef, {
        totalExpenses: increment(expenseData.amount),
        updatedAt: serverTimestamp()
      });
    }
    
    await batch.commit();
    
    // Log activity with debt information
    const otherMembers = expenseData.splitBetween.filter(id => id !== expenseData.paidBy);
    const debtAmount = otherMembers.reduce((total, memberId) => {
      return total + (expenseData.splitDetails?.[memberId] || 0);
    }, 0);

    await activityService.log({
      userId: expenseData.paidBy,
      action: 'added_expense',
      entityType: 'expense',
      entityId: expenseRef.id,
      details: `Added expense: ${expenseData.title} - ₹${expenseData.amount}${otherMembers.length > 0 ? ` (₹${debtAmount} owed by others)` : ''}`,
      metadata: {
        amount: expenseData.amount,
        category: expenseData.category,
        debtAmount: debtAmount,
        splitBetween: expenseData.splitBetween.length,
        groupId: expenseData.groupId
      }
    });
    
    return expenseRef.id;
  },

  async getGroupExpenses(groupId: string): Promise<Expense[]> {
    const expensesRef = collection(db, COLLECTIONS.EXPENSES);
    const q = query(
      expensesRef,
      where('groupId', '==', groupId)
    );

    const snapshot = await getDocs(q);
    const expenses = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date?.toDate() || new Date(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Expense[];

    // Filter and sort in memory to avoid index requirements
    return expenses
      .filter(expense => expense.type === 'group')
      .sort((a, b) => b.date.getTime() - a.date.getTime());
  },

  async getUserExpenses(userId: string): Promise<Expense[]> {
    const expensesRef = collection(db, COLLECTIONS.EXPENSES);
    const q = query(
      expensesRef,
      where('splitBetween', 'array-contains', userId),
      orderBy('date', 'desc'),
      limit(100)
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date?.toDate() || new Date(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Expense[];
  },

  async updateExpense(id: string, updates: Partial<Omit<Expense, 'id' | 'createdAt'>>) {
    const expenseRef = doc(db, COLLECTIONS.EXPENSES, id);
    await updateDoc(expenseRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    // Log activity
    const expense = await this.get(id);
    if (expense) {
      await activityService.log({
        userId: expense.paidBy,
        action: 'updated_expense',
        entityType: 'expense',
        entityId: id,
        details: `Updated expense: ${expense.title}`,
        metadata: { updates }
      });
    }
  },

  async deleteExpense(id: string) {
    const expense = await this.get(id);
    if (!expense) return;
    
    const batch = writeBatch(db);
    
    // Delete expense
    const expenseRef = doc(db, COLLECTIONS.EXPENSES, id);
    batch.delete(expenseRef);
    
    // Update group total if it's a group expense
    if (expense.groupId) {
      const groupRef = doc(db, COLLECTIONS.GROUPS, expense.groupId);
      batch.update(groupRef, {
        totalExpenses: increment(-expense.amount),
        updatedAt: serverTimestamp()
      });
    }
    
    await batch.commit();
    
    // Log activity
    await activityService.log({
      userId: expense.paidBy,
      action: 'deleted_expense',
      entityType: 'expense',
      entityId: id,
      details: `Deleted expense: ${expense.title} - ₹${expense.amount}`,
      metadata: {
        amount: expense.amount,
        category: expense.category
      }
    });
  },

  async get(id: string): Promise<Expense | null> {
    const expenseRef = doc(db, COLLECTIONS.EXPENSES, id);
    const expenseSnap = await getDoc(expenseRef);
    
    if (expenseSnap.exists()) {
      const data = expenseSnap.data();
      return {
        id: expenseSnap.id,
        ...data,
        date: data.date?.toDate() || new Date(),
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Expense;
    }
    return null;
  },

  onGroupExpensesSnapshot(groupId: string, callback: (expenses: Expense[]) => void) {
    const expensesRef = collection(db, COLLECTIONS.EXPENSES);
    const q = query(
      expensesRef,
      where('groupId', '==', groupId)
    );

    return onSnapshot(q, (snapshot) => {
      const expenses = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        date: doc.data().date?.toDate() || new Date(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as Expense[];

      // Filter and sort in memory to avoid index requirements
      const filteredExpenses = expenses
        .filter(expense => expense.type === 'group')
        .sort((a, b) => b.date.getTime() - a.date.getTime());

      callback(filteredExpenses);
    });
  }
};

export const personalExpenseService = {
  async create(expenseData: Omit<PersonalExpense, 'id' | 'createdAt' | 'updatedAt'>) {
    const expensesRef = collection(db, COLLECTIONS.PERSONAL_EXPENSES);
    const docRef = await addDoc(expensesRef, {
      ...expenseData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    // Log activity
    await activityService.log({
      userId: expenseData.userId,
      action: 'added_personal_expense',
      entityType: 'personal_expense',
      entityId: docRef.id,
      details: `Added personal expense: ${expenseData.title} - ₹${expenseData.amount}`,
      metadata: {
        amount: expenseData.amount,
        category: expenseData.category,
        month: expenseData.month
      }
    });
    
    return docRef.id;
  },

  async getUserExpenses(userId: string, month?: string): Promise<PersonalExpense[]> {
    const expensesRef = collection(db, COLLECTIONS.PERSONAL_EXPENSES);
    const q = query(
      expensesRef,
      where('userId', '==', userId)
    );

    const snapshot = await getDocs(q);
    const expenses = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date?.toDate() || new Date(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as PersonalExpense[];

    // Filter and sort in memory to avoid index requirements
    let filteredExpenses = expenses;
    if (month) {
      filteredExpenses = expenses.filter(expense => expense.month === month);
    }

    return filteredExpenses.sort((a, b) => b.date.getTime() - a.date.getTime());
  },

  async update(id: string, updates: Partial<Omit<PersonalExpense, 'id' | 'createdAt'>>) {
    const expenseRef = doc(db, COLLECTIONS.PERSONAL_EXPENSES, id);
    await updateDoc(expenseRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  },

  async delete(id: string) {
    const expense = await this.get(id);
    if (!expense) return;
    
    const expenseRef = doc(db, COLLECTIONS.PERSONAL_EXPENSES, id);
    await deleteDoc(expenseRef);
    
    // Log activity
    await activityService.log({
      userId: expense.userId,
      action: 'deleted_personal_expense',
      entityType: 'personal_expense',
      entityId: id,
      details: `Deleted personal expense: ${expense.title} - ₹${expense.amount}`,
      metadata: {
        amount: expense.amount,
        category: expense.category
      }
    });
  },

  async get(id: string): Promise<PersonalExpense | null> {
    const expenseRef = doc(db, COLLECTIONS.PERSONAL_EXPENSES, id);
    const expenseSnap = await getDoc(expenseRef);
    
    if (expenseSnap.exists()) {
      const data = expenseSnap.data();
      return {
        id: expenseSnap.id,
        ...data,
        date: data.date?.toDate() || new Date(),
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as PersonalExpense;
    }
    return null;
  },

  onUserExpensesSnapshot(userId: string, callback: (expenses: PersonalExpense[]) => void) {
    const expensesRef = collection(db, COLLECTIONS.PERSONAL_EXPENSES);
    const q = query(
      expensesRef,
      where('userId', '==', userId),
      limit(100)
    );

    return onSnapshot(q, (snapshot) => {
      const expenses = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        date: doc.data().date?.toDate() || new Date(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as PersonalExpense[];

      // Sort in memory to avoid index requirements
      const sortedExpenses = expenses.sort((a, b) => b.date.getTime() - a.date.getTime());
      callback(sortedExpenses);
    });
  }
};

// Predefined categories
export const EXPENSE_CATEGORIES: ExpenseCategory[] = [
  'hotel',
  'juice',
  'petrol',
  'cloth',
  'medicine',
  'bills',
  'food',
  'transport',
  'entertainment',
  'shopping',
  'utilities',
  'other'
];

export const getCategoryIcon = (category: ExpenseCategory): string => {
  const icons: Record<ExpenseCategory, string> = {
    hotel: '🏨',
    juice: '🧃',
    petrol: '⛽',
    cloth: '👕',
    medicine: '💊',
    bills: '📄',
    food: '🍽️',
    transport: '🚗',
    entertainment: '🎬',
    shopping: '🛍️',
    utilities: '💡',
    other: '📦'
  };
  return icons[category] || '📦';
};
