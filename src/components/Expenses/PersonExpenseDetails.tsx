import React from 'react';
import { X, Calendar, Tag, User, DollarSign, Receipt } from 'lucide-react';
import { getCategoryIcon } from '../../services/expenseService';

interface PersonExpenseDetailsProps {
  expense: {
    id: string;
    title: string;
    amount: number;
    date: Date;
    category: string;
    description?: string;
    splitType: 'equal' | 'manual' | 'percentage' | 'shares';
    splitDetails: Record<string, number>;
    paidBy: string;
    splitWith: string[];
  };
  people: Array<{
    id: string;
    name: string;
    email?: string;
  }>;
  currentUserId: string;
  onClose: () => void;
}

const PersonExpenseDetails: React.FC<PersonExpenseDetailsProps> = ({
  expense,
  people,
  currentUserId,
  onClose
}) => {
  const formatCurrency = (amount: number) => `₹${Math.abs(amount).toLocaleString()}`;
  
  // Get payer information
  const paidByPerson = people.find(p => p.id === expense.paidBy);
  const currentUserShare = expense.splitDetails[currentUserId] || 0;
  const isPaidByCurrentUser = expense.paidBy === currentUserId;
  
  // Calculate who owes what
  const splitBreakdown = Object.entries(expense.splitDetails).map(([personId, amount]) => {
    const person = people.find(p => p.id === personId);
    const isPayer = personId === expense.paidBy;
    
    return {
      person,
      amount,
      isPayer,
      owesAmount: isPayer ? 0 : amount,
      isOwedAmount: isPayer ? expense.amount - amount : 0
    };
  }).filter(item => item.person);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Expense Details</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          >
            <X size={20} className="text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        <div className="p-4 space-y-4">
          {/* Expense Info */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-12 h-12 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                <span className="text-xl">{getCategoryIcon(expense.category)}</span>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{expense.title}</h3>
                <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                  <div className="flex items-center gap-1">
                    <Calendar size={14} />
                    <span>{expense.date.toLocaleDateString('en-IN', { 
                      day: 'numeric', 
                      month: 'short', 
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Tag size={14} />
                    <span className="capitalize">{expense.category}</span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(expense.amount)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                  {expense.splitType} split
                </div>
              </div>
            </div>
            
            {expense.description && (
              <div className="mt-3 p-3 bg-white dark:bg-gray-600 rounded border">
                <p className="text-sm text-gray-700 dark:text-gray-300">{expense.description}</p>
              </div>
            )}
          </div>

          {/* Paid By */}
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign size={16} className="text-green-600 dark:text-green-400" />
              <h4 className="font-medium text-green-900 dark:text-green-100">Paid By</h4>
            </div>
            <div className="flex items-center gap-2">
              <User size={14} className="text-green-700 dark:text-green-300" />
              <span className="text-green-700 dark:text-green-300">
                {paidByPerson?.name || 'Unknown'} paid {formatCurrency(expense.amount)}
              </span>
            </div>
          </div>

          {/* Split Details */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-3">
              <Receipt size={16} className="text-blue-600 dark:text-blue-400" />
              <h4 className="font-medium text-blue-900 dark:text-blue-100">Split Breakdown</h4>
            </div>
            
            <div className="space-y-2">
              {splitBreakdown.map((item) => (
                <div key={item.person?.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                      <User size={12} className="text-blue-600 dark:text-blue-300" />
                    </div>
                    <span className="text-blue-700 dark:text-blue-300">
                      {item.person?.name}
                      {item.person?.id === currentUserId && ' (You)'}
                    </span>
                  </div>
                  <div className="text-blue-700 dark:text-blue-300 font-medium">
                    {formatCurrency(item.amount)}
                    {item.isPayer && (
                      <span className="ml-1 text-xs text-green-600 dark:text-green-400">(Paid)</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Your Balance */}
          {currentUserShare > 0 && (
            <div className={`rounded-lg p-3 border ${
              isPaidByCurrentUser 
                ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                : 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800'
            }`}>
              <h4 className={`font-medium mb-2 ${
                isPaidByCurrentUser 
                  ? 'text-green-900 dark:text-green-100'
                  : 'text-orange-900 dark:text-orange-100'
              }`}>
                Your Balance
              </h4>
              
              {isPaidByCurrentUser ? (
                <div className="space-y-1">
                  <div className="text-sm text-green-700 dark:text-green-300">
                    You paid {formatCurrency(expense.amount)}
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300">
                    Your share: {formatCurrency(currentUserShare)}
                  </div>
                  <div className="text-sm font-semibold text-green-700 dark:text-green-300">
                    You're owed {formatCurrency(expense.amount - currentUserShare)}
                  </div>
                </div>
              ) : (
                <div className="text-sm font-semibold text-orange-700 dark:text-orange-300">
                  You owe {formatCurrency(currentUserShare)} to {paidByPerson?.name}
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <button
              onClick={onClose}
              className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
            {!isPaidByCurrentUser && currentUserShare > 0 && (
              <button className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                Settle Up
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonExpenseDetails;
