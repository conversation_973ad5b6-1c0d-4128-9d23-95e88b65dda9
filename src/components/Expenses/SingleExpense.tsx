import React, { useState } from 'react';
import { Receipt, Plus, Edit, Trash2, Calendar, Tag } from 'lucide-react';
import { usePersonalExpenses } from '../../hooks/useExpenses';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../UI/LoadingSpinner';
import AddSingleExpenseModal from './AddSingleExpenseModal';
import { getCategoryIcon } from '../../services/expenseService';

const SingleExpense: React.FC = () => {
  const { currentUser } = useAuth();
  const { expenses, loading, error } = usePersonalExpenses(); // Use personal expenses instead
  const [showAddExpense, setShowAddExpense] = useState(false);

  if (loading) {
    return <LoadingSpinner text="Loading expenses..." />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  // Personal expenses are already filtered, no need to filter again
  const singleExpenses = expenses;

  return (
    <div className="space-y-4 pb-20">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">Single Expenses</h1>
          <p className="text-sm text-gray-600 dark:text-gray-300">Individual expenses not shared with anyone</p>
        </div>
        <button
          onClick={() => setShowAddExpense(true)}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
        >
          <Plus size={16} />
          Add Expense
        </button>
      </div>

      {/* Expenses List */}
      {singleExpenses.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center">
          <Receipt size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Single Expenses Yet</h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Start tracking your individual expenses that don't belong to any group.
          </p>
          <button
            onClick={() => setShowAddExpense(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Add Your First Expense
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {singleExpenses.map((expense) => (
            <div key={expense.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <span className="text-lg">{getCategoryIcon(expense.category)}</span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white truncate">{expense.title}</h4>
                    <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
                      <div className="flex items-center gap-1">
                        <Calendar size={12} />
                        <span>{expense.date.toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Tag size={12} />
                        <span className="capitalize">{expense.category}</span>
                      </div>
                    </div>
                    {expense.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 truncate">{expense.description}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="font-semibold text-gray-900 dark:text-white">
                      ₹{expense.amount.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-300">
                      Single
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <button className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors">
                      <Edit size={14} className="text-gray-600 dark:text-gray-300" />
                    </button>
                    <button className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors">
                      <Trash2 size={14} className="text-red-600 dark:text-red-400" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Expense Modal */}
      {showAddExpense && (
        <AddSingleExpenseModal
          onClose={() => setShowAddExpense(false)}
          currentUserId={currentUser?.uid || ''}
        />
      )}
    </div>
  );
};

export default SingleExpense;
