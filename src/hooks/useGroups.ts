import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { groupService } from '../services/firestore';
import type { Group } from '../types';

export const useGroups = () => {
  const { currentUser } = useAuth();
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) {
      setGroups([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // Set up real-time listener
    const unsubscribe = groupService.onSnapshot(
      currentUser.uid,
      (updatedGroups) => {
        setGroups(updatedGroups);
        setLoading(false);
      }
    );

    // Handle errors (if needed in future)
    // const handleError = (err: Error) => {
    //   console.error('Error fetching groups:', err);
    //   setError('Failed to load groups');
    //   setLoading(false);
    // };

    // Cleanup function
    return () => {
      unsubscribe();
    };
  }, [currentUser]);

  const createGroup = async (groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setError(null);
      console.log('🔄 useGroups: Creating group...', groupData);
      const groupId = await groupService.create(groupData);
      console.log('✅ useGroups: Group created successfully:', groupId);
      return groupId;
    } catch (err: any) {
      console.error('❌ useGroups: Group creation failed:', err);
      const errorMessage = err?.message || 'Failed to create group';
      setError(errorMessage);
      throw err; // Throw the original error to preserve details
    }
  };

  const updateGroup = async (groupId: string, updates: Partial<Omit<Group, 'id' | 'createdAt'>>) => {
    try {
      setError(null);
      await groupService.update(groupId, updates);
    } catch (err) {
      const errorMessage = 'Failed to update group';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const addMember = async (groupId: string, userId: string) => {
    try {
      setError(null);
      await groupService.addMember(groupId, userId);
    } catch (err) {
      const errorMessage = 'Failed to add member';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const removeMember = async (groupId: string, userId: string) => {
    try {
      setError(null);
      await groupService.removeMember(groupId, userId);
    } catch (err) {
      const errorMessage = 'Failed to remove member';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  return {
    groups,
    loading,
    error,
    createGroup,
    updateGroup,
    addMember,
    removeMember,
    refetch: () => {
      if (currentUser) {
        setLoading(true);
        // The real-time listener will handle the update
      }
    }
  };
};
