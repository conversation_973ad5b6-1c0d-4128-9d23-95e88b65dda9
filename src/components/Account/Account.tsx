import React, { useState, useEffect } from 'react';
import { User, Settings, BarChart3, DollarSign, TrendingUp, TrendingDown } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { usePeopleExpenses } from '../../hooks/usePeopleExpenses';
import { useGroups } from '../../hooks/useGroups';
import { balanceService } from '../../services/balanceService';
import LoadingSpinner from '../UI/LoadingSpinner';

const Account: React.FC = () => {
  const { currentUser } = useAuth();
  const { allExpenses, loading: expensesLoading } = usePeopleExpenses();
  const { groups, loading: groupsLoading } = useGroups();
  const [summary, setSummary] = useState({
    totalSpent: 0,
    totalOwed: 0,
    totalOwing: 0,
    netBalance: 0,
    totalExpenses: 0,
    groupsCount: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const calculateSummary = async () => {
      if (!currentUser || expensesLoading || groupsLoading) return;

      try {
        setLoading(true);

        // Calculate expenses summary from all expenses
        let totalSpent = 0;
        let totalOwed = 0;
        let totalOwing = 0;
        let expensesCount = 0;

        // Filter expenses that involve current user
        const userExpenses = allExpenses.filter(expense =>
          expense.splitBetween?.includes(currentUser.uid) ||
          expense.paidBy === currentUser.uid ||
          (expense.splitAmounts && currentUser.uid in expense.splitAmounts)
        );

        userExpenses.forEach(expense => {
          expensesCount++;
          if (expense.paidBy === currentUser.uid) {
            totalSpent += expense.amount;
            // Amount others owe to current user
            Object.entries(expense.splitAmounts || {}).forEach(([userId, amount]) => {
              if (userId !== currentUser.uid) {
                totalOwed += amount;
              }
            });
          } else {
            // Amount current user owes
            const userShare = expense.splitAmounts?.[currentUser.uid] || 0;
            totalOwing += userShare;
          }
        });

        // Add group expenses to totals (they're already included in allExpenses)
        const netBalance = totalOwed - totalOwing;

        setSummary({
          totalSpent,
          totalOwed,
          totalOwing,
          netBalance,
          totalExpenses: expensesCount,
          groupsCount: groups.length
        });
      } catch (error) {
        console.error('Error calculating summary:', error);
      } finally {
        setLoading(false);
      }
    };

    calculateSummary();
  }, [currentUser, allExpenses, groups, expensesLoading, groupsLoading]);

  const formatCurrency = (amount: number) => `₹${Math.abs(amount).toLocaleString()}`;

  if (loading) {
    return <LoadingSpinner text="Loading account details..." />;
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-3 sm:p-0">
      {/* Header */}
      <div>
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Account Details</h1>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">Manage your account and view statistics</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <DollarSign size={16} className="text-blue-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">Total Spent</span>
          </div>
          <div className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(summary.totalSpent)}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp size={16} className="text-green-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">Owed to You</span>
          </div>
          <div className="text-lg sm:text-xl font-bold text-green-600 dark:text-green-400">
            {formatCurrency(summary.totalOwed)}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <TrendingDown size={16} className="text-red-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">You Owe</span>
          </div>
          <div className="text-lg sm:text-xl font-bold text-red-600 dark:text-red-400">
            {formatCurrency(summary.totalOwing)}
          </div>
        </div>

        <div className={`rounded-lg p-3 sm:p-4 border ${
          summary.netBalance >= 0
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
        }`}>
          <div className="flex items-center gap-2 mb-2">
            <BarChart3 size={16} className={summary.netBalance >= 0 ? 'text-green-600' : 'text-red-600'} />
            <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">Net Balance</span>
          </div>
          <div className={`text-lg sm:text-xl font-bold ${
            summary.netBalance >= 0
              ? 'text-green-600 dark:text-green-400'
              : 'text-red-600 dark:text-red-400'
          }`}>
            {summary.netBalance >= 0 ? '+' : ''}{formatCurrency(summary.netBalance)}
          </div>
        </div>
      </div>

      {/* Profile and Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3 mb-4">
            <User size={20} className="text-blue-600" />
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">Profile Information</h2>
          </div>

          <div className="space-y-3">
            <div>
              <label className="text-sm text-gray-600 dark:text-gray-300">Name</label>
              <p className="font-medium text-gray-900 dark:text-white">{currentUser?.displayName}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600 dark:text-gray-300">Email</label>
              <p className="font-medium text-gray-900 dark:text-white">{currentUser?.email}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600 dark:text-gray-300">Role</label>
              <p className="font-medium text-gray-900 dark:text-white">
                {currentUser?.isAdmin ? 'Admin' : 'User'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3 mb-4">
            <BarChart3 size={20} className="text-green-600" />
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">Activity Summary</h2>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-300">Total Expenses</span>
              <span className="font-semibold text-gray-900 dark:text-white">{summary.totalExpenses}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-300">Groups Joined</span>
              <span className="font-semibold text-gray-900 dark:text-white">{summary.groupsCount}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-300">Account Status</span>
              <span className={`font-semibold ${
                Math.abs(summary.netBalance) < 1
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-yellow-600 dark:text-yellow-400'
              }`}>
                {Math.abs(summary.netBalance) < 1 ? 'Settled' : 'Active'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Settings Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 sm:p-8 border border-gray-200 dark:border-gray-700 text-center">
        <Settings size={40} className="mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Account Settings</h3>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
          Additional account settings and detailed statistics will be available here.
        </p>
      </div>
    </div>
  );
};

export default Account;
